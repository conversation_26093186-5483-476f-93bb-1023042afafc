import Cocoa
import SwiftUI
import FlutterMacOS

@main
class AppDelegate: FlutterAppDelegate {
    // add this object
    var statusBarExtra: StatusBarExtraController?
    
    override func applicationDidFinishLaunching(_ notification: Notification) {
      // add these lines
      let statusBarContent = StatusBarContent()
      let mainExtraView = NSHostingView(rootView: statusBarContent)
      mainExtraView.frame =  NSRect(x: 0, y: 0, width: 120, height: 200)
      statusBarExtra = StatusBarExtraController(mainExtraView)
    }

    override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return false
    }
}
