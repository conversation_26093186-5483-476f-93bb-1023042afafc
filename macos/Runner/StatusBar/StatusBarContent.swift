import Foundation
import SwiftUI
import FlutterMacOS
import Cocoa
import AppKit

struct StatusBarContent: View {
    var body: some View {
        VStack {
            Text("SPen Server")

            Button("Terminate", action: {
                NSApplication.shared.terminate(self)
            })
        }
        .padding()
    }
}
struct StatusBarContent_Previews: PreviewProvider {
    static var previews: some View {
        StatusBarContent()
    }
}
