import Foundation
import AppKit
import SwiftUI
import Cocoa

class StatusBarExtraController {
    private var statusBar: NSStatusBar
    private var statusItem: NSStatusItem
    private var mainView: NSView

    init(_ view: NSView) {
        self.mainView = view
        statusBar = NSStatusBar()
        statusItem = statusBar.statusItem(withLength: NSStatusItem.variableLength)

        let iconSwiftUI = ZStack(alignment:.center) {
                 Rectangle()
                     .fill(Color.green)
                     .cornerRadius(10)
                     .padding(0)
             }

            let iconView = NSHostingView(rootView: iconSwiftUI)
            iconView.frame = NSRect(x: 0, y: 0, width: 15, height: 15)

            if let statusBarButton = statusItem.button {
                statusBarButton.frame = iconView.frame

                // Create NSImage from Assets/draw.png
                if let image = NSImage(named: "draw") {
                  let newSize = NSSize(width: 18, height: 18) // Adjust this to your needs
                  let resizedImage = NSImage(size: newSize)
                  resizedImage.lockFocus()
                  image.draw(in: NSRect(origin: .zero, size: newSize), from: .zero, operation: .copy, fraction: 1.0)
                  resizedImage.unlockFocus()
                  statusBarButton.image = resizedImage
                }

                let menuItem = NSMenuItem()
                menuItem.view = mainView
                let menu = NSMenu()

                menu.addItem(menuItem)
                statusItem.menu = menu
            }
    }
}
