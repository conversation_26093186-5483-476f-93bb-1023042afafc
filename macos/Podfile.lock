PODS:
  - bonsoir_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - macos_window_utils (1.0.0):
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS
  - window_size (0.0.2):
    - FlutterMacOS

DEPENDENCIES:
  - bonsoir_darwin (from `Flutter/ephemeral/.symlinks/plugins/bonsoir_darwin/darwin`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - macos_window_utils (from `Flutter/ephemeral/.symlinks/plugins/macos_window_utils/macos`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)
  - window_size (from `Flutter/ephemeral/.symlinks/plugins/window_size/macos`)

EXTERNAL SOURCES:
  bonsoir_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/bonsoir_darwin/darwin
  FlutterMacOS:
    :path: Flutter/ephemeral
  macos_window_utils:
    :path: Flutter/ephemeral/.symlinks/plugins/macos_window_utils/macos
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos
  window_size:
    :path: Flutter/ephemeral/.symlinks/plugins/window_size/macos

SPEC CHECKSUMS:
  bonsoir_darwin: 29c7ccf356646118844721f36e1de4b61f6cbd0e
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  macos_window_utils: 3bca8603c2a1cf2257351dfe6bbccc9accf739fd
  screen_retriever_macos: 452e51764a9e1cdb74b3c541238795849f21557f
  window_manager: 1d01fa7ac65a6e6f83b965471b1a7fdd3f06166c
  window_size: 4bd15034e6e3d0720fd77928a7c42e5492cfece9

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.16.2
