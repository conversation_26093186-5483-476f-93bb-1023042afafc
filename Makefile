.DEFAULT_GOAL := help

help: ## show help message
	@echo "Draw It Application - Available Commands:"
	@echo ""
	@echo "🖥️  Server Commands (Desktop):"
	@echo "  make server          - Run the server on macOS (desktop mode)"
	@echo "  make server-debug    - Run the server with debug logging"
	@echo "  make server-release  - Run the server in release mode"
	@echo ""
	@echo "📱 Client Commands (Mobile/Drawer):"
	@echo "  make drawer          - Run the drawer/client mode"
	@echo "  make client          - Run the client mode on macOS (simulated mobile)"
	@echo "  make android-client  - Build and run on Android device/emulator"
	@echo "  make ios-client      - Build and run on iOS device/simulator"
	@echo ""
	@echo "🔧 Development Commands:"
	@awk 'BEGIN {FS = ":.*##"; printf ""} /^[$$()% 0-9a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
	@echo ""
	@echo "📋 Platform Detection:"
	@echo "  - Desktop (macOS/Linux/Windows) = Server Mode"
	@echo "  - Mobile (Android/iOS) = Client Mode"
	@echo "  - Override with FORCE_APP_MODE=client or FORCE_APP_MODE=server"

##@ 🖥️ Server Commands

server: ## run the server on macOS (desktop mode)
	@echo "🖥️  Starting Draw It Server on macOS..."
	@echo "📡 Server will advertise on the local network for clients to discover"
	@echo "🎨 Drawing canvas will be displayed for receiving client drawings"
	@echo ""
	flutter run -d macos --debug

server-debug: ## run the server with debug logging
	@echo "🖥️  Starting Draw It Server with debug logging..."
	flutter run -d macos --debug --verbose

server-release: ## run the server in release mode
	@echo "🖥️  Starting Draw It Server in release mode..."
	flutter run -d macos --release

##@ 📱 Client Commands

drawer: client ## alias for client mode (drawer)

client: ## run the client/drawer mode on macOS (simulated mobile)
	@echo "📱 Starting Draw It Client (Drawer) on macOS..."
	@echo "🔍 Client will search for available servers on the network"
	@echo "✏️  Drawing interface will send strokes to connected server"
	@echo ""
	@echo "⚠️  Note: Running client mode on desktop for testing purposes"
	@echo "    For best experience, use 'make android-client' or 'make ios-client'"
	@echo ""
	FORCE_APP_MODE=client flutter run -d macos --debug

android-client: ## build and run on Android device/emulator
	@echo "📱 Building and running Draw It Client on Android..."
	@echo "🔍 Make sure an Android device is connected or emulator is running"
	@echo ""
	@flutter devices | grep android || (echo "❌ No Android devices found. Please connect a device or start an emulator." && exit 1)
	flutter run -d android --debug

ios-client: ## build and run on iOS device/simulator
	@echo "📱 Building and running Draw It Client on iOS..."
	@echo "🔍 Make sure an iOS device is connected or simulator is running"
	@echo ""
	@flutter devices | grep ios || (echo "❌ No iOS devices found. Please connect a device or start a simulator." && exit 1)
	flutter run -d ios --debug

##@ 🔧 Development Commands

check_lint: ## check if there are any lint issues
	flutter analyze --no-pub --no-fatal-infos --no-fatal-warnings

format: ## autoformat code
	flutter format lib/main.dart lib/src/ test/

clean: ## clean all autogenerated files
	## Delete all generated files by hand
	find . -maxdepth 20 -type f \( -name "*.inject.summary" -o -name "*.inject.dart" -o -name "*.g.dart" -o -name "*.mocks.dart" -o -name "*.chopper.dart" \) -delete

	## Cleanup
	flutter pub run build_runner clean

	flutter clean

clean_macos: ## delete all iOS local configuration files so flutter can regenerate them if there's a change on dependencies
	rm -rf macos/Pods/ || true
	rm macos/Podfile.lock || true

	flutter clean
	flutter pub get

	cd ios &&  pod repo update

build_macos: ## build macOS app
	flutter build macos --release

build_windows: ## build windows app
	flutter build windows

build_linux: ## build linux app
	flutter build linux

install_linux: ## install linux app
	flutter build linux --release
	sudo cp -r build/linux/x64/release/bundle/ /opt/draw_it

	sudo rm -f /usr/local/bin/draw_it

	sudo ln -sf /opt/draw_it/draw_it /usr/local/bin/draw_it
	sudo cp draw_it.desktop /usr/share/applications/

##@ 📱 Additional Commands

devices: ## show available devices
	@echo "📱 Available devices:"
	flutter devices

deps: ## install dependencies
	@echo "📦 Installing Flutter dependencies..."
	flutter pub get
	@echo "✅ Dependencies installed!"

doctor: ## run Flutter doctor
	@echo "🩺 Running Flutter doctor..."
	flutter doctor -v

test: ## run tests
	@echo "🧪 Running tests..."
	flutter test

build-all: ## build for all platforms
	@echo "🔨 Building Draw It for all platforms..."
	@echo ""
	@echo "📱 Building Android APK..."
	flutter build apk --debug
	@echo ""
	@echo "🖥️  Building macOS app..."
	flutter build macos --debug
	@echo ""
	@echo "✅ Build complete!"

start: server ## quick start (alias for server)

draw: drawer ## quick draw (alias for drawer)

info: ## show app information
	@echo "📋 Draw It Application Information:"
	@echo ""
	@echo "🏗️  Architecture:"
	@echo "   - Server Mode: Desktop platforms (macOS, Linux, Windows)"
	@echo "   - Client Mode: Mobile platforms (Android, iOS)"
	@echo ""
	@echo "🌐 Network:"
	@echo "   - Server advertises via mDNS/Bonjour on _http._tcp"
	@echo "   - Client discovers servers automatically"
	@echo "   - Communication via HTTP REST API"
	@echo ""
	@echo "🎨 Features:"
	@echo "   - Real-time drawing synchronization"
	@echo "   - Multi-client support"
	@echo "   - Cross-platform compatibility"

##@ 🚀 Quick Commands

run-server: server ## quick server start

run-client: client ## quick client start

dev: ## development mode with hot reload (server)
	@echo "🚀 Starting development server with hot reload..."
	flutter run -d macos --debug --hot

dev-client: ## development mode with hot reload (client)
	@echo "🚀 Starting development client with hot reload..."
	FORCE_APP_MODE=client flutter run -d macos --debug --hot

network-test: ## test network connectivity and mDNS
	@echo "🌐 Testing network connectivity..."
	@echo "📡 Checking mDNS/Bonjour services..."
	@command -v dns-sd >/dev/null 2>&1 && dns-sd -B _http._tcp || echo "⚠️  dns-sd not available (install Bonjour SDK)"
	@echo ""
	@echo "🔍 Checking for existing Draw It servers..."
	@command -v dns-sd >/dev/null 2>&1 && timeout 5 dns-sd -B _http._tcp | grep -i "draw it" || echo "ℹ️  No Draw It servers currently advertising"

release-android: ## build Android release APK
	@echo "📱 Building Android release APK..."
	flutter build apk --release
	@echo "✅ Android release APK built: build/app/outputs/flutter-apk/app-release.apk"

release-macos: ## build macOS release app
	@echo "🖥️  Building macOS release app..."
	flutter build macos --release
	@echo "✅ macOS release app built: build/macos/Build/Products/Release/draw_it.app"