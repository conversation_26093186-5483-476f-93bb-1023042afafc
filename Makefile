.DEFAULT_GOAL := help

help: ## show help message
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m\033[0m\n"} /^[$$()% 0-9a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

check_lint: ## check if there are any lint issues
	flutter analyze --no-pub --no-fatal-infos --no-fatal-warnings

format: ## autoformat code
	flutter format lib/main.dart lib/src/ test/

clean: ## clean all autogenerated files
	## Delete all generated files by hand
	find . -maxdepth 20 -type f \( -name "*.inject.summary" -o -name "*.inject.dart" -o -name "*.g.dart" -o -name "*.mocks.dart" -o -name "*.chopper.dart" \) -delete

	## Cleanup
	flutter pub run build_runner clean

	flutter clean

clean_macos: ## delete all iOS local configuration files so flutter can regenerate them if there's a change on dependencies
	rm -rf macos/Pods/ || true
	rm macos/Podfile.lock || true

	flutter clean
	flutter pub get

	cd ios &&  pod repo update

build_macos: ## build macOS app
	flutter build macos --release

build_windows: ## build windows app
	flutter build windows

build_linux: ## build linux app
	flutter build linux

install_linux: ## install linux app
	flutter build linux --release
	sudo cp -r build/linux/x64/release/bundle/ /opt/draw_it

	sudo rm -f /usr/local/bin/draw_it

	sudo ln -sf /opt/draw_it/draw_it /usr/local/bin/draw_it
	sudo cp draw_it.desktop /usr/share/applications/