on: workflow_dispatch

name: Build Mac

# Cancel jobs and just run the last one
concurrency:
  group: ${{ github.head_ref }}-deploy-to-staging
  cancel-in-progress: true

jobs:
  macos:
    runs-on: macos-latest-large
    steps:
      - uses: actions/checkout@v3
      - name: Prepare flutter version from pubspec.yml
        run: echo "FLUTTER_VERSION=$(grep 'flutter:' pubspec.yaml | awk '{print $2}')" >> $GITHUB_ENV

      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{env.FLUTTER_VERSION}}
          cache: false

      - run: flutter config --enable-macos-desktop
      - run: flutter pub get
      - run: flutter build macos --release

      - name: Archive
        uses: actions/upload-artifact@v4
        with:
          name: macos
          path: build/macos/Build/Products/Release/draw_it.app
