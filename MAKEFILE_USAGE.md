# Draw It - Makefile Usage Guide

This document explains how to use the Makefile commands to run the Draw It application in different modes.

## Quick Start

### Running the Server (Desktop)
```bash
make server
```
This starts the Draw It server on macOS. The server will:
- Display a drawing canvas to receive drawings from clients
- Advertise itself on the local network via mDNS/Bonjour
- Listen for HTTP connections from mobile clients

### Running the Drawer/Client (Mobile)
```bash
make drawer
```
or
```bash
make client
```
This starts the Draw It client/drawer mode. The client will:
- Search for available servers on the network
- Display a drawing interface to send strokes to the server
- Connect to discovered servers automatically

## Available Commands

### 🖥️ Server Commands (Desktop)

| Command | Description |
|---------|-------------|
| `make server` | Run the server on macOS (desktop mode) |
| `make server-debug` | Run the server with debug logging |
| `make server-release` | Run the server in release mode |

### 📱 Client Commands (Mobile/Drawer)

| Command | Description |
|---------|-------------|
| `make drawer` | Alias for client mode (drawer) |
| `make client` | Run the client/drawer mode on macOS (simulated mobile) |
| `make android-client` | Build and run on Android device/emulator |
| `make ios-client` | Build and run on iOS device/simulator |

### 🔧 Development Commands

| Command | Description |
|---------|-------------|
| `make deps` | Install Flutter dependencies |
| `make doctor` | Run Flutter doctor |
| `make test` | Run tests |
| `make check_lint` | Check for lint issues |
| `make format` | Auto-format code |
| `make clean` | Clean build artifacts |
| `make build-all` | Build for all platforms |

### 🚀 Quick Commands

| Command | Description |
|---------|-------------|
| `make start` | Quick server start (alias for server) |
| `make draw` | Quick client start (alias for drawer) |
| `make dev` | Development mode with hot reload (server) |
| `make dev-client` | Development mode with hot reload (client) |
| `make run-server` | Quick server start |
| `make run-client` | Quick client start |

### 📱 Platform-Specific Commands

| Command | Description |
|---------|-------------|
| `make devices` | Show available devices |
| `make release-android` | Build Android release APK |
| `make release-macos` | Build macOS release app |
| `make build_windows` | Build Windows app |
| `make build_linux` | Build Linux app |

### 🌐 Network Testing

| Command | Description |
|---------|-------------|
| `make network-test` | Test network connectivity and mDNS |

## Platform Detection

The application automatically detects the platform and sets the appropriate mode:

- **Desktop** (macOS, Linux, Windows) → **Server Mode**
- **Mobile** (Android, iOS) → **Client Mode**

### Overriding Platform Detection

You can override the automatic platform detection using environment variables:

```bash
# Force client mode on desktop (for testing)
FORCE_APP_MODE=client flutter run -d macos

# Force server mode
FORCE_APP_MODE=server flutter run -d macos

# Legacy environment variables (also supported)
FORCE_CLIENT_MODE=true flutter run -d macos
FORCE_SERVER_MODE=true flutter run -d macos
```

The Makefile automatically handles this for the `client` command on macOS.

## Typical Workflow

### 1. Development Setup
```bash
make deps          # Install dependencies
make doctor        # Check Flutter setup
```

### 2. Running Server and Client
```bash
# Terminal 1: Start the server
make server

# Terminal 2: Start the client (for testing on same machine)
make client

# Or on Android device
make android-client
```

### 3. Development with Hot Reload
```bash
# Server development
make dev

# Client development
make dev-client
```

### 4. Building for Production
```bash
make release-macos     # Build macOS server app
make release-android   # Build Android client APK
```

## Network Architecture

- **Server**: Advertises via mDNS/Bonjour on `_http._tcp` service type
- **Client**: Discovers servers automatically using service discovery
- **Communication**: HTTP REST API for drawing data transmission
- **Port**: Default port 8080 (configurable)

## Troubleshooting

### No Servers Found
```bash
make network-test    # Test mDNS connectivity
make devices         # Check available devices
```

### Build Issues
```bash
make clean          # Clean build artifacts
make deps           # Reinstall dependencies
make doctor         # Check Flutter setup
```

### Platform Issues
```bash
make info           # Show app information
make help           # Show all available commands
```

## Examples

### Basic Usage
```bash
# Start server
make server

# In another terminal, start client
make drawer
```

### Development Workflow
```bash
# Start development server with hot reload
make dev

# In another terminal, start development client
make dev-client
```

### Android Testing
```bash
# Make sure Android device is connected
make devices

# Run on Android
make android-client
```

### Production Build
```bash
# Build release versions
make release-macos
make release-android

# Clean everything first if needed
make clean
make build-all
```

For more information, run `make help` to see all available commands.
