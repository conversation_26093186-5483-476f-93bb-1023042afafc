import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_acrylic/flutter_acrylic.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:draw_it/core/platform/platform_detector.dart';
import 'package:draw_it/core/platform/app_mode.dart';
import 'package:draw_it/features/server/pages/server_drawing_page.dart';
import 'package:draw_it/features/client/pages/client_drawing_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize window for desktop platforms
  if (Platform.isLinux || Platform.isMacOS || Platform.isWindows) {
    await Window.initialize();
  }

  runApp(const ProviderScope(child: DrawItApp()));
}

class DrawItApp extends ConsumerWidget {
  const DrawItApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appMode = ref.read(appModeProvider);

    return MaterialApp(
      title: 'Draw It',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: DrawItHomePage(mode: appMode),
    );
  }
}

class DrawItHomePage extends StatefulWidget {
  const DrawItHomePage({super.key, required this.mode});

  final AppMode mode;

  @override
  State<DrawItHomePage> createState() => _DrawItHomePageState();
}

class _DrawItHomePageState extends State<DrawItHomePage> {
  @override
  void initState() {
    super.initState();

    // Initialize desktop-specific features for server mode
    if (widget.mode == AppMode.server) {
      // Window initialization is handled in main() for desktop platforms
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.mode == AppMode.server
      ? const ServerDrawingPage()
      : const ClientDrawingPage();
  }
}

final log = Logger(
  printer: PrettyPrinter(
      methodCount: 0, // Number of method calls to be displayed
      errorMethodCount: 8, // Number of method calls if stacktrace is provided
      lineLength: 120, // Width of the output
      colors: true, // Colorful log messages
      printEmojis: true, // Print an emoji for each log message
      dateTimeFormat: DateTimeFormat.none // No timestamp
      ),
);
