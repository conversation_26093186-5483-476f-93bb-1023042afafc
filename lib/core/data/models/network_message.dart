import 'package:flutter/material.dart';
import 'drawing_point.dart';

/// Represents a network message for drawing communication
/// Compatible with the existing HTTP protocol
class NetworkMessage {
  const NetworkMessage({
    required this.type,
    required this.sessionId,
    this.point,
    this.screenSize,
    this.payload,
  });

  /// Type of the message
  final NetworkMessageType type;
  
  /// Session/draw ID for grouping related points
  final String sessionId;
  
  /// Drawing point data (for drawing messages)
  final DrawingPoint? point;
  
  /// Screen size information
  final Size? screenSize;
  
  /// Additional payload data
  final Map<String, dynamic>? payload;

  /// Create from JSON (compatible with existing protocol)
  factory NetworkMessage.fromJson(Map<String, dynamic> json) {
    final payloadData = json['payload'] as Map<String, dynamic>?;
    
    return NetworkMessage(
      type: NetworkMessageType.fromString(json['type'] as String),
      sessionId: json['session_id'] as String? ?? '',
      point: payloadData?['point'] != null 
        ? DrawingPoint.fromJson(payloadData!['point'] as Map<String, dynamic>)
        : null,
      screenSize: payloadData?['screen_size'] != null
        ? _sizeFromJson(payloadData!['screen_size'] as Map<String, dynamic>)
        : null,
      payload: payloadData,
    );
  }

  /// Convert to JSON (compatible with existing protocol)
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> payloadData = {};
    
    if (point != null) {
      payloadData['point'] = point!.toLegacyJson();
    }
    
    if (screenSize != null) {
      payloadData['screen_size'] = {
        'width': screenSize!.width,
        'height': screenSize!.height,
      };
    }
    
    if (sessionId.isNotEmpty) {
      payloadData['draw_id'] = sessionId;
    }
    
    if (payload != null) {
      payloadData.addAll(payload!);
    }

    return {
      'payload': payloadData,
    };
  }

  /// Create a drawing message
  factory NetworkMessage.drawing({
    required String sessionId,
    required DrawingPoint point,
    required Size screenSize,
  }) {
    return NetworkMessage(
      type: NetworkMessageType.drawSingle,
      sessionId: sessionId,
      point: point,
      screenSize: screenSize,
    );
  }

  /// Create a touch down message
  factory NetworkMessage.touchDown({required String sessionId}) {
    return NetworkMessage(
      type: NetworkMessageType.touchDown,
      sessionId: sessionId,
    );
  }

  /// Create a touch up message
  factory NetworkMessage.touchUp({required String sessionId}) {
    return NetworkMessage(
      type: NetworkMessageType.touchUp,
      sessionId: sessionId,
    );
  }

  /// Create a screen command message
  factory NetworkMessage.command({required ScreenCommandType command}) {
    return NetworkMessage(
      type: NetworkMessageType.fromCommand(command),
      sessionId: '',
    );
  }

  static Size _sizeFromJson(Map<String, dynamic> json) {
    return Size(
      (json['width'] as num).toDouble(),
      (json['height'] as num).toDouble(),
    );
  }

  @override
  String toString() {
    return 'NetworkMessage(type: $type, sessionId: $sessionId, point: $point)';
  }
}

/// Types of network messages
enum NetworkMessageType {
  drawSingle,
  touchDown,
  touchUp,
  clean,
  show,
  hide,
  showWhiteboard,
  hideWhiteboard;

  /// Get the HTTP endpoint for this message type
  String get endpoint {
    switch (this) {
      case NetworkMessageType.drawSingle:
        return 'draw_single';
      case NetworkMessageType.touchDown:
        return 'touch_down';
      case NetworkMessageType.touchUp:
        return 'touch_up';
      case NetworkMessageType.clean:
        return 'clean';
      case NetworkMessageType.show:
        return 'show';
      case NetworkMessageType.hide:
        return 'hide';
      case NetworkMessageType.showWhiteboard:
        return 'show_whiteboard';
      case NetworkMessageType.hideWhiteboard:
        return 'hide_whiteboard';
    }
  }

  /// Create from string (for parsing HTTP endpoints)
  static NetworkMessageType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'draw_single':
        return NetworkMessageType.drawSingle;
      case 'touch_down':
        return NetworkMessageType.touchDown;
      case 'touch_up':
        return NetworkMessageType.touchUp;
      case 'clean':
        return NetworkMessageType.clean;
      case 'show':
        return NetworkMessageType.show;
      case 'hide':
        return NetworkMessageType.hide;
      case 'show_whiteboard':
        return NetworkMessageType.showWhiteboard;
      case 'hide_whiteboard':
        return NetworkMessageType.hideWhiteboard;
      default:
        throw ArgumentError('Unknown message type: $value');
    }
  }

  /// Create from screen command type
  static NetworkMessageType fromCommand(ScreenCommandType command) {
    switch (command) {
      case ScreenCommandType.clean:
        return NetworkMessageType.clean;
      case ScreenCommandType.show:
        return NetworkMessageType.show;
      case ScreenCommandType.hide:
        return NetworkMessageType.hide;
      case ScreenCommandType.showWhiteboard:
        return NetworkMessageType.showWhiteboard;
      case ScreenCommandType.hideWhiteboard:
        return NetworkMessageType.hideWhiteboard;
    }
  }
}

/// Screen command types
enum ScreenCommandType {
  clean,
  show,
  hide,
  showWhiteboard,
  hideWhiteboard;
}
