/// Represents a discovered server on the network
class DiscoveredServer {
  const DiscoveredServer({
    required this.name,
    required this.host,
    required this.port,
    this.attributes = const {},
  });

  /// Server name (from Bonjour service)
  final String name;
  
  /// Server IP address
  final String host;
  
  /// Server port
  final int port;
  
  /// Additional attributes from service discovery
  final Map<String, String> attributes;

  /// Create from Bonjour service discovery
  factory DiscoveredServer.fromService({
    required String name,
    required String host,
    required int port,
    Map<String, dynamic>? serviceAttributes,
  }) {
    final attributes = <String, String>{};
    
    if (serviceAttributes != null) {
      for (final entry in serviceAttributes.entries) {
        if (entry.value is String) {
          attributes[entry.key] = entry.value as String;
        } else if (entry.value != null) {
          attributes[entry.key] = entry.value.toString();
        }
      }
    }
    
    return DiscoveredServer(
      name: name,
      host: host,
      port: port,
      attributes: attributes,
    );
  }

  /// Get the full server URL
  String get url => 'http://$host:$port';

  /// Get server description for UI display
  String get displayName => '$name ($host:$port)';

  /// Check if this server matches the expected service type
  bool get isDrawItServer => name.toLowerCase().contains('draw it');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DiscoveredServer &&
        other.host == host &&
        other.port == port;
  }

  @override
  int get hashCode => Object.hash(host, port);

  @override
  String toString() {
    return 'DiscoveredServer(name: $name, host: $host, port: $port)';
  }
}
