import 'package:flutter/material.dart';

/// Represents a single drawing point with position, pressure, and color information
/// This model is compatible with the existing network protocol
class DrawingPoint {
  const DrawingPoint({
    required this.x,
    required this.y,
    required this.pressure,
    required this.colorHex,
    this.timestamp,
  });

  /// X coordinate of the point
  final double x;

  /// Y coordinate of the point
  final double y;

  /// Pressure value (0.0 to 1.0)
  final double pressure;

  /// Color in hex format (#AARRGGBB)
  final String colorHex;

  /// Optional timestamp for the point
  final DateTime? timestamp;

  /// Create a DrawingPoint from JSON (network protocol compatible)
  factory DrawingPoint.fromJson(Map<String, dynamic> json) {
    return DrawingPoint(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      pressure: (json['pressure'] as num).toDouble(),
      colorHex: json['color'] as String,
      timestamp: json['timestamp'] != null
        ? DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int)
        : null,
    );
  }

  /// Convert to JSON (network protocol compatible)
  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'pressure': pressure,
      'color': colorHex,
      if (timestamp != null) 'timestamp': timestamp!.millisecondsSinceEpoch,
    };
  }

  /// Convert to the legacy Point model for compatibility
  Map<String, dynamic> toLegacyJson() {
    return {
      'x': x,
      'y': y,
      'pressure': pressure,
      'color': colorHex,
    };
  }

  /// Get the Flutter Color object from hex string
  Color get color {
    return Color(int.parse(colorHex.substring(1), radix: 16));
  }

  /// Calculate stroke width based on pressure (matching server logic)
  double get strokeWidth {
    final int multiplier;
    if (pressure < 0.5) {
      multiplier = 4;
    } else if (pressure < 0.7) {
      multiplier = 8;
    } else {
      multiplier = 10;
    }
    return multiplier * pressure;
  }

  /// Create a copy with modified properties
  DrawingPoint copyWith({
    double? x,
    double? y,
    double? pressure,
    String? colorHex,
    DateTime? timestamp,
  }) {
    return DrawingPoint(
      x: x ?? this.x,
      y: y ?? this.y,
      pressure: pressure ?? this.pressure,
      colorHex: colorHex ?? this.colorHex,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Transform point coordinates between different screen sizes
  DrawingPoint transformToSize(Size sourceSize, Size targetSize) {
    final newX = (x / sourceSize.width) * targetSize.width;
    final newY = (y / sourceSize.height) * targetSize.height;

    return copyWith(x: newX, y: newY);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DrawingPoint &&
        other.x == x &&
        other.y == y &&
        other.pressure == pressure &&
        other.colorHex == colorHex;
  }

  @override
  int get hashCode {
    return Object.hash(x, y, pressure, colorHex);
  }

  @override
  String toString() {
    return 'DrawingPoint(x: $x, y: $y, pressure: $pressure, color: $colorHex)';
  }
}

/// Utility functions for working with drawing points
class DrawingPointUtils {
  /// Convert Flutter Color to hex string (compatible with Android client)
  static String colorToHex(Color color) {
    final argb = (color.a * 255).round() << 24 |
                 (color.r * 255).round() << 16 |
                 (color.g * 255).round() << 8 |
                 (color.b * 255).round();
    return '#${argb.toRadixString(16).padLeft(8, '0').toUpperCase()}';
  }

  /// Parse hex color string to Flutter Color
  static Color hexToColor(String hex) {
    return Color(int.parse(hex.substring(1), radix: 16));
  }

  /// Create a DrawingPoint from touch input
  static DrawingPoint fromTouch({
    required double x,
    required double y,
    required double pressure,
    required Color color,
  }) {
    return DrawingPoint(
      x: x,
      y: y,
      pressure: pressure,
      colorHex: colorToHex(color),
      timestamp: DateTime.now(),
    );
  }
}
