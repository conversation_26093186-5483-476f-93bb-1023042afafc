import 'dart:async';
import 'package:flutter/material.dart';
import 'package:draw_it/main.dart';
import 'drawing_point_handler.dart';
import '../data/models/drawing_point.dart';
import '../data/models/network_message.dart';
import '../data/models/discovered_server.dart';
import '../network/http_client_service.dart';

/// Client implementation of drawing point handler
/// Sends drawing points to a network server
class ClientDrawingPointHandler extends BaseDrawingPointHandler {
  ClientDrawingPointHandler();

  late final HttpClientNetworkService _networkService;
  DiscoveredServer? _connectedServer;
  final List<DiscoveredServer> _discoveredServers = [];

  @override
  Future<void> initializeImplementation() async {
    _networkService = HttpClientNetworkService();
    log.d('[ClientPointHandler] Initialized');
  }

  /// Start discovering servers on the network
  Stream<DiscoveredServer> discoverServers() {
    return _networkService.discoverServers();
  }

  /// Connect to a specific server
  Future<bool> connectToServer(DiscoveredServer server) async {
    try {
      final success = await _networkService.connectToServer(server);
      if (success) {
        _connectedServer = server;
        log.d('[ClientPointHandler] Connected to server: ${server.displayName}');
      }
      return success;
    } catch (e, stack) {
      log.e('[ClientPointHandler] Failed to connect to server: $e', error: e, stackTrace: stack);
      return false;
    }
  }

  /// Disconnect from the current server
  Future<void> disconnect() async {
    _connectedServer = null;
    await _networkService.dispose();
    log.d('[ClientPointHandler] Disconnected from server');
  }

  @override
  Future<void> handlePoint(DrawingPoint point, String sessionId, Size screenSize) async {
    if (!isConnected) {
      log.w('[ClientPointHandler] Cannot send point - not connected to server');
      return;
    }

    try {
      final message = NetworkMessage.drawing(
        sessionId: sessionId,
        point: point,
        screenSize: screenSize,
      );

      await _networkService.sendMessage(message);
      log.d('[ClientPointHandler] Sent drawing point: ${point.x}, ${point.y}');
    } catch (e, stack) {
      log.e('[ClientPointHandler] Failed to send point: $e', error: e, stackTrace: stack);
    }
  }

  @override
  Future<void> handleTouchDown(String sessionId) async {
    if (!isConnected) {
      log.w('[ClientPointHandler] Cannot send touch down - not connected to server');
      return;
    }

    try {
      final message = NetworkMessage.touchDown(sessionId: sessionId);
      await _networkService.sendMessage(message);
      log.d('[ClientPointHandler] Sent touch down: $sessionId');
    } catch (e, stack) {
      log.e('[ClientPointHandler] Failed to send touch down: $e', error: e, stackTrace: stack);
    }
  }

  @override
  Future<void> handleTouchUp(String sessionId) async {
    if (!isConnected) {
      log.w('[ClientPointHandler] Cannot send touch up - not connected to server');
      return;
    }

    try {
      final message = NetworkMessage.touchUp(sessionId: sessionId);
      await _networkService.sendMessage(message);
      log.d('[ClientPointHandler] Sent touch up: $sessionId');
    } catch (e, stack) {
      log.e('[ClientPointHandler] Failed to send touch up: $e', error: e, stackTrace: stack);
    }
  }

  @override
  Future<void> handleScreenCommand(ScreenCommandType command) async {
    if (!isConnected) {
      log.w('[ClientPointHandler] Cannot send command - not connected to server');
      return;
    }

    try {
      final message = NetworkMessage.command(command: command);
      await _networkService.sendMessage(message);
      log.d('[ClientPointHandler] Sent command: $command');
    } catch (e, stack) {
      log.e('[ClientPointHandler] Failed to send command: $e', error: e, stackTrace: stack);
    }
  }

  @override
  bool get isConnected => _networkService.isConnected && _connectedServer != null;

  /// Get the currently connected server
  DiscoveredServer? get connectedServer => _connectedServer;

  /// Get the server URL if connected
  String? get serverUrl => _connectedServer?.url;

  @override
  Stream<DrawingPoint> get pointStream {
    // Client mode doesn't receive points, it sends them
    return const Stream.empty();
  }

  @override
  Stream<ScreenCommandType> get commandStream {
    // Client mode doesn't receive commands, it sends them
    return const Stream.empty();
  }

  @override
  Stream<SessionEvent> get sessionStream {
    // Client mode doesn't receive session events, it sends them
    return const Stream.empty();
  }

  @override
  Future<void> disposeImplementation() async {
    await _networkService.dispose();
    _connectedServer = null;
    _discoveredServers.clear();
    log.d('[ClientPointHandler] Disposed');
  }
}
