import 'package:flutter/material.dart';
import '../data/models/drawing_point.dart';

/// Abstract interface for rendering drawing points
abstract class DrawingRenderer {
  /// Render drawing points on canvas
  void render(
    Canvas canvas,
    Size canvasSize,
    List<List<DrawingPoint>> completedStrokes,
    List<DrawingPoint> currentStroke,
  );

  /// Transform point coordinates between screen sizes
  DrawingPoint transformPoint(
    DrawingPoint point,
    Size sourceSize,
    Size targetSize,
  );

  /// Calculate stroke width based on pressure
  double calculateStrokeWidth(double pressure);

  /// Parse color from hex string
  Color parseColor(String hexColor);

  /// Convert color to hex string
  String colorToHex(Color color);
}

/// Default implementation of drawing renderer
class DrawingRendererImpl implements DrawingRenderer {
  const DrawingRendererImpl();

  @override
  void render(
    Canvas canvas,
    Size canvasSize,
    List<List<DrawingPoint>> completedStrokes,
    List<DrawingPoint> currentStroke,
  ) {
    final paint = Paint()
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    // <PERSON><PERSON> completed strokes
    for (final stroke in completedStrokes) {
      _renderStroke(canvas, stroke, paint);
    }

    // Render current stroke
    if (currentStroke.isNotEmpty) {
      _renderStroke(canvas, currentStroke, paint);
    }
  }

  void _renderStroke(Canvas canvas, List<DrawingPoint> points, Paint paint) {
    if (points.isEmpty) return;

    if (points.length == 1) {
      // Single point - draw a small circle
      final point = points.first;
      paint
        ..color = parseColor(point.colorHex)
        ..strokeWidth = calculateStrokeWidth(point.pressure)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(point.x, point.y),
        calculateStrokeWidth(point.pressure) / 2,
        paint,
      );
      return;
    }

    // Multiple points - draw lines between them
    paint.style = PaintingStyle.stroke;

    for (int i = 0; i < points.length - 1; i++) {
      final start = points[i];
      final end = points[i + 1];

      paint
        ..color = parseColor(start.colorHex)
        ..strokeWidth = calculateStrokeWidth(start.pressure);

      canvas.drawLine(
        Offset(start.x, start.y),
        Offset(end.x, end.y),
        paint,
      );
    }
  }

  @override
  DrawingPoint transformPoint(
    DrawingPoint point,
    Size sourceSize,
    Size targetSize,
  ) {
    if (sourceSize == targetSize) return point;

    final newX = (point.x / sourceSize.width) * targetSize.width;
    final newY = (point.y / sourceSize.height) * targetSize.height;

    return point.copyWith(x: newX, y: newY);
  }

  @override
  double calculateStrokeWidth(double pressure) {
    // Match the existing server logic
    final int multiplier;
    if (pressure < 0.5) {
      multiplier = 4;
    } else if (pressure < 0.7) {
      multiplier = 8;
    } else {
      multiplier = 10;
    }
    return multiplier * pressure;
  }

  @override
  Color parseColor(String hexColor) {
    try {
      // Remove # if present
      final hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;
      return Color(int.parse(hex, radix: 16));
    } catch (e) {
      // Default to black if parsing fails
      return Colors.black;
    }
  }

  @override
  String colorToHex(Color color) {
    final argb = (color.a * 255).round() << 24 |
                 (color.r * 255).round() << 16 |
                 (color.g * 255).round() << 8 |
                 (color.b * 255).round();
    return '#${argb.toRadixString(16).padLeft(8, '0').toUpperCase()}';
  }
}

/// Custom painter for drawing on canvas
class DrawingCanvasPainter extends CustomPainter {
  const DrawingCanvasPainter({
    required this.completedStrokes,
    required this.currentStroke,
    required this.sourceSize,
    required this.targetSize,
    this.renderer = const DrawingRendererImpl(),
  });

  final List<List<DrawingPoint>> completedStrokes;
  final List<DrawingPoint> currentStroke;
  final Size sourceSize;
  final Size targetSize;
  final DrawingRenderer renderer;

  @override
  void paint(Canvas canvas, Size size) {
    // Transform strokes if needed
    final transformedCompleted = completedStrokes.map((stroke) {
      return stroke.map((point) {
        return renderer.transformPoint(point, sourceSize, targetSize);
      }).toList();
    }).toList();

    final transformedCurrent = currentStroke.map((point) {
      return renderer.transformPoint(point, sourceSize, targetSize);
    }).toList();

    // Render the drawing
    renderer.render(
      canvas,
      size,
      transformedCompleted,
      transformedCurrent,
    );
  }

  @override
  bool shouldRepaint(covariant DrawingCanvasPainter oldDelegate) {
    return completedStrokes != oldDelegate.completedStrokes ||
           currentStroke != oldDelegate.currentStroke ||
           sourceSize != oldDelegate.sourceSize ||
           targetSize != oldDelegate.targetSize;
  }
}
