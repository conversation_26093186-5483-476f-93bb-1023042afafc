import 'dart:async';
import 'package:flutter/material.dart';
import 'package:draw_it/main.dart';
import 'drawing_point_handler.dart';
import '../data/models/drawing_point.dart';
import '../data/models/network_message.dart';
import '../network/network_service.dart';
import '../network/http_server_service.dart';

/// Server implementation of drawing point handler
/// Receives drawing points from network clients and processes them
class ServerDrawingPointHandler extends BaseDrawingPointHandler {
  ServerDrawingPointHandler({
    required this.port,
    this.serviceName = 'draw it server',
  });

  final int port;
  final String serviceName;
  
  late final HttpServerNetworkService _networkService;
  StreamSubscription<NetworkMessage>? _messageSubscription;
  final Map<String, String> _connectedClients = {};

  @override
  Future<void> initializeImplementation() async {
    _networkService = HttpServerNetworkService();
    
    // Start the HTTP server
    await _networkService.startServer(
      port: port,
      onClientConnected: _handleClientConnected,
      onClientDisconnected: _handleClientDisconnected,
    );

    // Start advertising the service
    await _networkService.startAdvertising(serviceName);

    // Listen for incoming messages
    _messageSubscription = _networkService.incomingMessages.listen(_handleNetworkMessage);

    log.d('[ServerPointHandler] Initialized on port $port');
  }

  void _handleClientConnected(String clientId) {
    _connectedClients[clientId] = clientId;
    log.d('[ServerPointHandler] Client connected: $clientId');
  }

  void _handleClientDisconnected(String clientId) {
    _connectedClients.remove(clientId);
    log.d('[ServerPointHandler] Client disconnected: $clientId');
  }

  void _handleNetworkMessage(NetworkMessage message) {
    log.d('[ServerPointHandler] Received message: ${message.type}');

    switch (message.type) {
      case NetworkMessageType.drawSingle:
        if (message.point != null) {
          emitPoint(message.point!);
        }
        break;
      
      case NetworkMessageType.touchDown:
        emitSessionEvent(SessionEventType.touchDown, message.sessionId);
        break;
      
      case NetworkMessageType.touchUp:
        emitSessionEvent(SessionEventType.touchUp, message.sessionId);
        break;
      
      case NetworkMessageType.clean:
        emitCommand(ScreenCommandType.clean);
        break;
      
      case NetworkMessageType.show:
        emitCommand(ScreenCommandType.show);
        break;
      
      case NetworkMessageType.hide:
        emitCommand(ScreenCommandType.hide);
        break;
      
      case NetworkMessageType.showWhiteboard:
        emitCommand(ScreenCommandType.showWhiteboard);
        break;
      
      case NetworkMessageType.hideWhiteboard:
        emitCommand(ScreenCommandType.hideWhiteboard);
        break;
    }
  }

  @override
  Future<void> handlePoint(DrawingPoint point, String sessionId, Size screenSize) async {
    // Server mode receives points from network, doesn't generate them locally
    log.w('[ServerPointHandler] handlePoint called on server - this should not happen');
  }

  @override
  Future<void> handleTouchDown(String sessionId) async {
    // Server mode receives touch events from network
    log.w('[ServerPointHandler] handleTouchDown called on server - this should not happen');
  }

  @override
  Future<void> handleTouchUp(String sessionId) async {
    // Server mode receives touch events from network
    log.w('[ServerPointHandler] handleTouchUp called on server - this should not happen');
  }

  @override
  Future<void> handleScreenCommand(ScreenCommandType command) async {
    // Server mode receives commands from network
    log.w('[ServerPointHandler] handleScreenCommand called on server - this should not happen');
  }

  /// Get the number of connected clients
  int get connectedClientsCount => _connectedClients.length;

  /// Get the list of connected client IDs
  List<String> get connectedClientIds => _connectedClients.keys.toList();

  /// Get the server URL
  String? get serverUrl => _networkService.serverUrl;

  @override
  Future<void> disposeImplementation() async {
    await _messageSubscription?.cancel();
    await _networkService.dispose();
    _connectedClients.clear();
    log.d('[ServerPointHandler] Disposed');
  }
}
