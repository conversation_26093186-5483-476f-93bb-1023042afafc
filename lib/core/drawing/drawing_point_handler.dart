import 'dart:async';
import 'package:flutter/material.dart';
import '../data/models/drawing_point.dart';
import '../data/models/network_message.dart';

/// Abstract interface for handling drawing points
/// Provides different implementations for server and client modes
abstract class DrawingPointHandler {
  /// Initialize the handler for the current mode
  Future<void> initialize();

  /// Handle a drawing point (send for client, process for server)
  Future<void> handlePoint(DrawingPoint point, String sessionId, Size screenSize);

  /// Handle touch down event
  Future<void> handleTouchDown(String sessionId);

  /// Handle touch up event
  Future<void> handleTouchUp(String sessionId);

  /// Handle screen commands (clean, show/hide, etc.)
  Future<void> handleScreenCommand(ScreenCommandType command);

  /// Stream of drawing points (for server mode)
  Stream<DrawingPoint> get pointStream;

  /// Stream of screen commands (for server mode)
  Stream<ScreenCommandType> get commandStream;

  /// Stream of session events (touch up/down)
  Stream<SessionEvent> get sessionStream;

  /// Get current connection status
  bool get isConnected;

  /// Dispose resources
  Future<void> dispose();
}

/// Represents a drawing session event
class SessionEvent {
  const SessionEvent({
    required this.type,
    required this.sessionId,
  });

  final SessionEventType type;
  final String sessionId;

  @override
  String toString() {
    return 'SessionEvent(type: $type, sessionId: $sessionId)';
  }
}

/// Types of session events
enum SessionEventType {
  touchDown,
  touchUp,
}

/// Base implementation with common functionality
abstract class BaseDrawingPointHandler implements DrawingPointHandler {
  final StreamController<DrawingPoint> _pointController = StreamController.broadcast();
  final StreamController<ScreenCommandType> _commandController = StreamController.broadcast();
  final StreamController<SessionEvent> _sessionController = StreamController.broadcast();

  bool _isInitialized = false;
  bool _isDisposed = false;

  @override
  Stream<DrawingPoint> get pointStream => _pointController.stream;

  @override
  Stream<ScreenCommandType> get commandStream => _commandController.stream;

  @override
  Stream<SessionEvent> get sessionStream => _sessionController.stream;

  @override
  bool get isConnected => _isInitialized && !_isDisposed;

  /// Emit a drawing point to listeners
  void emitPoint(DrawingPoint point) {
    if (!_isDisposed) {
      _pointController.add(point);
    }
  }

  /// Emit a screen command to listeners
  void emitCommand(ScreenCommandType command) {
    if (!_isDisposed) {
      _commandController.add(command);
    }
  }

  /// Emit a session event to listeners
  void emitSessionEvent(SessionEventType type, String sessionId) {
    if (!_isDisposed) {
      _sessionController.add(SessionEvent(type: type, sessionId: sessionId));
    }
  }

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    await initializeImplementation();
    _isInitialized = true;
  }

  /// Implementation-specific initialization
  Future<void> initializeImplementation();

  @override
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;
    await disposeImplementation();

    await _pointController.close();
    await _commandController.close();
    await _sessionController.close();
  }

  /// Implementation-specific disposal
  Future<void> disposeImplementation();
}
