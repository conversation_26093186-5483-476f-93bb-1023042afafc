import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_mode.dart';
import 'platform_config.dart';

/// Interface for detecting the current platform and determining app mode
abstract class PlatformDetector {
  /// Determine the appropriate app mode for the current platform
  AppMode get appMode;
  
  /// Get platform-specific configuration
  PlatformConfig get config;
  
  /// Whether the current platform is a desktop platform
  bool get isDesktop;
  
  /// Whether the current platform is a mobile platform
  bool get isMobile;
  
  /// Whether the current platform supports window management
  bool get supportsWindowManagement;
}

/// Implementation of platform detection logic
class PlatformDetectorImpl implements PlatformDetector {
  const PlatformDetectorImpl();
  
  @override
  AppMode get appMode {
    if (isDesktop) {
      return AppMode.server;
    } else if (isMobile) {
      return AppMode.client;
    } else {
      // Default to server mode for unknown platforms (e.g., web)
      return AppMode.server;
    }
  }
  
  @override
  PlatformConfig get config {
    switch (appMode) {
      case AppMode.server:
        return ServerPlatformConfig(
          enableWindowManagement: supportsWindowManagement,
          enableTransparency: Platform.isLinux || Platform.isMacOS,
        );
      case AppMode.client:
        return ClientPlatformConfig(
          enableStylusSupport: Platform.isAndroid,
          enableHandDrawing: true,
        );
    }
  }
  
  @override
  bool get isDesktop {
    return Platform.isLinux || Platform.isMacOS || Platform.isWindows;
  }
  
  @override
  bool get isMobile {
    return Platform.isAndroid || Platform.isIOS;
  }
  
  @override
  bool get supportsWindowManagement {
    return isDesktop;
  }
  
  /// Get the current platform name for debugging
  String get platformName {
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isLinux) return 'Linux';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isFuchsia) return 'Fuchsia';
    return 'Unknown';
  }
}

/// Provider for platform detection
final platformDetectorProvider = Provider<PlatformDetector>((ref) {
  return const PlatformDetectorImpl();
});

/// Provider for the current app mode
final appModeProvider = Provider<AppMode>((ref) {
  final detector = ref.read(platformDetectorProvider);
  return detector.appMode;
});

/// Provider for platform configuration
final platformConfigProvider = Provider<PlatformConfig>((ref) {
  final detector = ref.read(platformDetectorProvider);
  return detector.config;
});
