import 'dart:io';

/// Base configuration for platform-specific settings
abstract class PlatformConfig {
  const PlatformConfig();
  
  /// Default network port for the application
  int get defaultPort;
  
  /// Whether the platform supports network discovery
  bool get supportsNetworkDiscovery;
  
  /// Whether the platform supports stylus input
  bool get supportsStylusInput;
  
  /// Whether the platform supports finger drawing
  bool get supportsFingerDrawing;
  
  /// Maximum number of concurrent connections (server mode only)
  int? get maxConnections;
}

/// Configuration for server mode (desktop platforms)
class ServerPlatformConfig extends PlatformConfig {
  const ServerPlatformConfig({
    this.port = 8080,
    this.enableWindowManagement = true,
    this.enableTransparency = true,
    this.maxConcurrentConnections = 10,
  });
  
  final int port;
  final bool enableWindowManagement;
  final bool enableTransparency;
  final int maxConcurrentConnections;
  
  @override
  int get defaultPort => port;
  
  @override
  bool get supportsNetworkDiscovery => true;
  
  @override
  bool get supportsStylusInput => false;
  
  @override
  bool get supportsFingerDrawing => false;
  
  @override
  int get maxConnections => maxConcurrentConnections;
  
  /// Whether window transparency is supported on this platform
  bool get canUseTransparency {
    return enableTransparency && (Platform.isLinux || Platform.isMacOS);
  }
}

/// Configuration for client mode (mobile platforms)
class ClientPlatformConfig extends PlatformConfig {
  const ClientPlatformConfig({
    this.enableStylusSupport = true,
    this.enableHandDrawing = true,
    this.discoveryTimeoutSeconds = 10,
    this.reconnectAttempts = 3,
    this.connectionTimeoutSeconds = 5,
  });
  
  final bool enableStylusSupport;
  final bool enableHandDrawing;
  final int discoveryTimeoutSeconds;
  final int reconnectAttempts;
  final int connectionTimeoutSeconds;
  
  @override
  int get defaultPort => 8080;
  
  @override
  bool get supportsNetworkDiscovery => true;
  
  @override
  bool get supportsStylusInput => enableStylusSupport;
  
  @override
  bool get supportsFingerDrawing => enableHandDrawing;
  
  @override
  int? get maxConnections => null;
  
  /// Timeout duration for server discovery
  Duration get discoveryTimeout => Duration(seconds: discoveryTimeoutSeconds);
  
  /// Timeout duration for connection attempts
  Duration get connectionTimeout => Duration(seconds: connectionTimeoutSeconds);
  
  /// Whether stylus support is available on this platform
  bool get hasStylusSupport {
    return enableStylusSupport && Platform.isAndroid;
  }
}
