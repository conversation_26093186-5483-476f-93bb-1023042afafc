/// Defines the operational mode of the application
enum AppMode {
  /// Server mode - runs on desktop platforms (Linux, Windows, macOS)
  /// Receives drawing points from clients and displays them
  server,
  
  /// Client mode - runs on mobile platforms (Android, iOS)
  /// Sends drawing points to a server
  client,
}

extension AppModeExtension on AppMode {
  /// Human-readable name for the mode
  String get displayName {
    switch (this) {
      case AppMode.server:
        return 'Server Mode';
      case AppMode.client:
        return 'Client Mode';
    }
  }
  
  /// Whether this mode can receive drawing data
  bool get canReceive => this == AppMode.server;
  
  /// Whether this mode can send drawing data
  bool get canSend => this == AppMode.client;
  
  /// Whether this mode supports window management
  bool get supportsWindowManagement => this == AppMode.server;
  
  /// Whether this mode supports network discovery
  bool get supportsNetworkDiscovery => true;
}
