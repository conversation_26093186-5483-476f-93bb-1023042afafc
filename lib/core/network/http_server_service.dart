import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:bonsoir/bonsoir.dart';
import 'package:draw_it/main.dart';
import 'network_service.dart';
import '../data/models/network_message.dart';
import '../data/models/discovered_server.dart';
import '../data/models/drawing_point.dart';

/// HTTP server implementation for server mode (desktop platforms)
class HttpServerNetworkService extends BaseNetworkService {
  HttpServer? _server;
  BonsoirBroadcast? _broadcast;
  int _connectedClients = 0;
  String? _serverUrl;

  @override
  Future<void> startServer({
    required int port,
    required Function(String clientId) onClientConnected,
    required Function(String clientId) onClientDisconnected,
  }) async {
    try {
      // Stop any existing server
      await _stopServer();

      // Start HTTP server
      _server = await HttpServer.bind(InternetAddress.anyIPv6, port);
      _serverUrl = 'http://${await _getLocalIP()}:$port';

      log.d('[HttpServer] Server started on $_serverUrl');
      setServerRunning(true);

      // Listen for HTTP requests
      _server!.listen((request) async {
        await _handleRequest(request, onClientConnected, onClientDisconnected);
      });

    } catch (e, stack) {
      log.e('[HttpServer] Failed to start server: $e', error: e, stackTrace: stack);
      setServerRunning(false);
      rethrow;
    }
  }

  Future<void> _handleRequest(
    HttpRequest request,
    Function(String clientId) onClientConnected,
    Function(String clientId) onClientDisconnected,
  ) async {
    try {
      final clientId = request.connectionInfo?.remoteAddress.address ?? 'unknown';

      // Read request body
      final body = await utf8.decodeStream(request);
      final endpoint = request.uri.path.substring(1); // Remove leading '/'

      log.d('[HttpServer] ${request.method} /$endpoint from $clientId');

      // Parse message based on endpoint
      final message = _parseRequest(endpoint, body, clientId);

      if (message != null) {
        // Notify about client connection if this is a new client
        onClientConnected(clientId);

        // Emit the message to listeners
        emitMessage(message);
      }

      // Send response (matching existing protocol)
      request.response
        ..statusCode = 202
        ..headers.contentType = ContentType('text', 'application/json', charset: 'utf-8')
        ..write('');

      await request.response.close();

    } catch (e, stack) {
      log.e('[HttpServer] Error handling request: $e', error: e, stackTrace: stack);

      request.response
        ..statusCode = 500
        ..write('Internal Server Error');

      await request.response.close();
    }
  }

  NetworkMessage? _parseRequest(String endpoint, String body, String clientId) {
    try {
      // Handle endpoints that don't require a body
      switch (endpoint) {
        case 'clean':
          return NetworkMessage.command(command: ScreenCommandType.clean);
        case 'show':
          return NetworkMessage.command(command: ScreenCommandType.show);
        case 'hide':
          return NetworkMessage.command(command: ScreenCommandType.hide);
        case 'show_whiteboard':
          return NetworkMessage.command(command: ScreenCommandType.showWhiteboard);
        case 'hide_whiteboard':
          return NetworkMessage.command(command: ScreenCommandType.hideWhiteboard);
      }

      // Handle endpoints that require a body
      if (body.isEmpty) {
        log.w('[HttpServer] Empty body for endpoint: $endpoint');
        return null;
      }

      final json = jsonDecode(body) as Map<String, dynamic>;
      final payload = json['payload'] as Map<String, dynamic>?;

      if (payload == null) {
        log.w('[HttpServer] Missing payload in request');
        return null;
      }

      switch (endpoint) {
        case 'draw_single':
          return _parseDrawSingleMessage(payload);
        case 'touch_down':
          return _parseTouchDownMessage(payload);
        case 'touch_up':
          return _parseTouchUpMessage(payload);
        default:
          log.w('[HttpServer] Unknown endpoint: $endpoint');
          return null;
      }
    } catch (e, stack) {
      log.e('[HttpServer] Error parsing request: $e', error: e, stackTrace: stack);
      return null;
    }
  }

  NetworkMessage _parseDrawSingleMessage(Map<String, dynamic> payload) {
    final sessionId = payload['draw_id'] as String;
    final pointData = payload['point'] as Map<String, dynamic>;
    final screenSizeData = payload['screen_size'] as Map<String, dynamic>;

    return NetworkMessage.drawing(
      sessionId: sessionId,
      point: DrawingPoint.fromJson(pointData),
      screenSize: Size(
        (screenSizeData['width'] as num).toDouble(),
        (screenSizeData['height'] as num).toDouble(),
      ),
    );
  }

  NetworkMessage _parseTouchDownMessage(Map<String, dynamic> payload) {
    final sessionId = payload['draw_id'] as String;
    return NetworkMessage.touchDown(sessionId: sessionId);
  }

  NetworkMessage _parseTouchUpMessage(Map<String, dynamic> payload) {
    final sessionId = payload['draw_id'] as String;
    return NetworkMessage.touchUp(sessionId: sessionId);
  }

  @override
  Future<void> startAdvertising(String serviceName) async {
    try {
      await stopAdvertising();

      final ip = await _getLocalIP();
      final service = BonsoirService(
        name: serviceName,
        type: '_http._tcp',
        port: _server?.port ?? 8080,
        attributes: {'ip': ip},
      );

      _broadcast = BonsoirBroadcast(service: service, printLogs: true);
      await _broadcast!.ready;
      await _broadcast!.start();

      log.d('[HttpServer] Started advertising: $serviceName');
    } catch (e, stack) {
      log.e('[HttpServer] Failed to start advertising: $e', error: e, stackTrace: stack);
    }
  }

  @override
  Future<void> stopAdvertising() async {
    if (_broadcast != null) {
      await _broadcast!.stop();
      _broadcast = null;
      log.d('[HttpServer] Stopped advertising');
    }
  }

  Future<void> _stopServer() async {
    if (_server != null) {
      await _server!.close(force: true);
      _server = null;
      _serverUrl = null;
      setServerRunning(false);
      log.d('[HttpServer] Server stopped');
    }
  }

  Future<String> _getLocalIP() async {
    final interfaces = await NetworkInterface.list();

    return interfaces
        .firstWhere(
          (i) => i.addresses.any((a) => a.type == InternetAddressType.IPv4),
        )
        .addresses
        .firstWhere((a) => a.type == InternetAddressType.IPv4)
        .address;
  }

  @override
  String? get serverUrl => _serverUrl;

  @override
  int get connectedClientsCount => _connectedClients;

  @override
  Future<bool> connectToServer(DiscoveredServer server) async {
    throw UnsupportedError('Server mode cannot connect to other servers');
  }

  @override
  Future<void> sendMessage(NetworkMessage message) async {
    throw UnsupportedError('Server mode sends responses, not messages');
  }

  @override
  Future<void> disposeImplementation() async {
    await stopAdvertising();
    await _stopServer();
  }
}
