import 'dart:async';
import '../data/models/network_message.dart';
import '../data/models/discovered_server.dart';

/// Abstract interface for network communication
/// Provides different implementations for server and client modes
abstract class NetworkService {
  /// Start server mode (desktop platforms)
  Future<void> startServer({
    required int port,
    required Function(String clientId) onClientConnected,
    required Function(String clientId) onClientDisconnected,
  });

  /// Connect to server (mobile platforms)
  Future<bool> connectToServer(DiscoveredServer server);

  /// Send network message to server/clients
  Future<void> sendMessage(NetworkMessage message);

  /// Stream of incoming network messages
  Stream<NetworkMessage> get incomingMessages;

  /// Stream of discovered servers (client mode)
  Stream<DiscoveredServer> discoverServers();

  /// Start advertising server presence
  Future<void> startAdvertising(String serviceName);

  /// Stop advertising server presence
  Future<void> stopAdvertising();

  /// Get connection status
  bool get isConnected;

  /// Get server status (for server mode)
  bool get isServerRunning;

  /// Get current server URL (for server mode)
  String? get serverUrl;

  /// Get connected clients count (for server mode)
  int get connectedClientsCount;

  /// Dispose resources
  Future<void> dispose();
}

/// Base implementation with common functionality
abstract class BaseNetworkService implements NetworkService {
  final StreamController<NetworkMessage> _messageController = StreamController.broadcast();
  final StreamController<DiscoveredServer> _discoveryController = StreamController.broadcast();

  bool _isConnected = false;
  bool _isServerRunning = false;
  bool _isDisposed = false;

  @override
  Stream<NetworkMessage> get incomingMessages => _messageController.stream;

  @override
  Stream<DiscoveredServer> discoverServers() => _discoveryController.stream;

  @override
  bool get isConnected => _isConnected && !_isDisposed;

  @override
  bool get isServerRunning => _isServerRunning && !_isDisposed;

  @override
  int get connectedClientsCount => 0; // Override in server implementation

  /// Emit an incoming message to listeners
  void emitMessage(NetworkMessage message) {
    if (!_isDisposed) {
      _messageController.add(message);
    }
  }

  /// Emit a discovered server to listeners
  void emitDiscoveredServer(DiscoveredServer server) {
    if (!_isDisposed) {
      _discoveryController.add(server);
    }
  }

  /// Set connection status
  void setConnected(bool connected) {
    _isConnected = connected;
  }

  /// Set server running status
  void setServerRunning(bool running) {
    _isServerRunning = running;
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;
    await stopAdvertising();
    await disposeImplementation();

    await _messageController.close();
    await _discoveryController.close();
  }

  /// Implementation-specific disposal
  Future<void> disposeImplementation();
}

/// Network service factory
abstract class NetworkServiceFactory {
  /// Create appropriate network service based on mode
  static NetworkService create({required bool isServerMode}) {
    if (isServerMode) {
      // Import will be resolved at runtime
      return _createServerService();
    } else {
      // Import will be resolved at runtime
      return _createClientService();
    }
  }

  static NetworkService _createServerService() {
    // This will be imported dynamically
    throw UnimplementedError('Use HttpServerNetworkService directly');
  }

  static NetworkService _createClientService() {
    // This will be imported dynamically
    throw UnimplementedError('Use HttpClientNetworkService directly');
  }
}
