import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:bonsoir/bonsoir.dart';
import 'package:draw_it/main.dart';
import 'network_service.dart';
import '../data/models/network_message.dart';
import '../data/models/discovered_server.dart';

/// HTTP client implementation for client mode (mobile platforms)
class HttpClientNetworkService extends BaseNetworkService {
  http.Client? _client;
  DiscoveredServer? _connectedServer;
  BonsoirDiscovery? _discovery;
  Timer? _discoveryTimer;

  @override
  Future<bool> connectToServer(DiscoveredServer server) async {
    try {
      // Test connection to server
      _client = http.Client();
      final response = await _client!
          .get(Uri.parse('${server.url}/'))
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 404) {
        // 404 is expected for root path, means server is running
        _connectedServer = server;
        setConnected(true);
        log.d('[HttpClient] Connected to server: ${server.displayName}');
        return true;
      } else {
        log.w(
            '[HttpClient] Unexpected response from server: ${response.statusCode}');
        return false;
      }
    } catch (e, stack) {
      log.e('[HttpClient] Failed to connect to server: $e',
          error: e, stackTrace: stack);
      setConnected(false);
      _connectedServer = null;
      _client?.close();
      _client = null;
      return false;
    }
  }

  @override
  Future<void> sendMessage(NetworkMessage message) async {
    if (!isConnected || _connectedServer == null || _client == null) {
      throw StateError('Not connected to server');
    }

    try {
      final url =
          Uri.parse('${_connectedServer!.url}/${message.type.endpoint}');
      final body = jsonEncode(message.toJson());

      log.d('[HttpClient] Sending ${message.type.endpoint}: $body');

      final response = await _client!
          .post(
            url,
            headers: {'Content-Type': 'application/json'},
            body: body,
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode != 202) {
        log.w('[HttpClient] Unexpected response: ${response.statusCode}');
      }
    } catch (e, stack) {
      log.e('[HttpClient] Failed to send message: $e',
          error: e, stackTrace: stack);

      // Check if we should disconnect
      if (e is SocketException || e is TimeoutException) {
        await _disconnect();
      }

      rethrow;
    }
  }

  @override
  Stream<DiscoveredServer> discoverServers() {
    _startDiscovery();
    return super.discoverServers();
  }

  void _startDiscovery() {
    if (_discovery != null) return;

    try {
      log.d('[HttpClient] Initializing service discovery...');
      _discovery = BonsoirDiscovery(type: '_http._tcp');

      _discovery!.ready.then((_) {
        log.d('[HttpClient] Discovery ready, starting scan...');
        _discovery!.start();
        log.d('[HttpClient] Started server discovery for _http._tcp services');
      }).catchError((error) {
        log.e('[HttpClient] Failed to start discovery: $error');
      });

      _discovery!.eventStream!.listen((event) {
        log.d('[HttpClient] Discovery event: ${event.type}');

        if (event.type == BonsoirDiscoveryEventType.discoveryServiceFound) {
          log.d('[HttpClient] Service found: ${event.service?.name}');
          _handleDiscoveredService(event.service!);
        } else if (event.type ==
            BonsoirDiscoveryEventType.discoveryServiceResolved) {
          log.d('[HttpClient] Service resolved: ${event.service?.name}');
          // Handle resolved service with complete information
          _handleResolvedService(event.service!);
        } else if (event.type ==
            BonsoirDiscoveryEventType.discoveryServiceLost) {
          log.d('[HttpClient] Service lost: ${event.service?.name}');
        }
      }, onError: (error) {
        log.e('[HttpClient] Discovery stream error: $error');
      });

      // Stop discovery after a timeout
      _discoveryTimer = Timer(const Duration(seconds: 30), () {
        log.d('[HttpClient] Discovery timeout reached, stopping...');
        _stopDiscovery();
      });
    } catch (e, stack) {
      log.e('[HttpClient] Failed to start discovery: $e',
          error: e, stackTrace: stack);
    }
  }

  void _handleDiscoveredService(BonsoirService service) {
    try {
      // Check if this is a Draw It server
      if (!service.name.toLowerCase().contains('draw it')) {
        return;
      }

      log.d(
          '[HttpClient] Service discovered: ${service.name}, port: ${service.port}');
      log.d('[HttpClient] Service attributes: ${service.attributes}');

      // Try to resolve the service to get complete information
      if (service.port == 0 || service.attributes.isEmpty) {
        log.d(
            '[HttpClient] Service incomplete, attempting resolution: ${service.name}');
        _resolveService(service);
        return;
      }

      // Process service with complete information
      _processCompleteService(service);
    } catch (e, stack) {
      log.e('[HttpClient] Error handling discovered service: $e',
          error: e, stackTrace: stack);
    }
  }

  void _handleResolvedService(BonsoirService service) {
    try {
      // Check if this is a Draw It server
      if (!service.name.toLowerCase().contains('draw it')) {
        return;
      }

      log.d(
          '[HttpClient] Service resolved: ${service.name}, port: ${service.port}');
      log.d('[HttpClient] Resolved attributes: ${service.attributes}');

      _processCompleteService(service);
    } catch (e, stack) {
      log.e('[HttpClient] Error handling resolved service: $e',
          error: e, stackTrace: stack);
    }
  }

  void _resolveService(BonsoirService service) {
    try {
      // Create a resolver for this specific service
      final resolver = BonsoirDiscovery(type: service.type);
      resolver.ready.then((_) {
        resolver.start();

        // Listen for resolution events
        resolver.eventStream?.listen((event) {
          if (event.type ==
                  BonsoirDiscoveryEventType.discoveryServiceResolved &&
              event.service?.name == service.name) {
            _handleResolvedService(event.service!);
            resolver.stop();
          }
        });

        // Stop resolver after timeout
        Timer(const Duration(seconds: 10), () {
          resolver.stop();
        });
      });
    } catch (e, stack) {
      log.e('[HttpClient] Error resolving service: $e',
          error: e, stackTrace: stack);
    }
  }

  void _processCompleteService(BonsoirService service) {
    try {
      String? host;
      int port = service.port;

      // Extract IP from attributes
      final ipData = service.attributes['ip'];
      if (ipData != null) {
        host = _convertAttributeToString(ipData);
      }

      // Fallback: try to extract host from service name or other attributes
      if (host == null || host.isEmpty) {
        log.w('[HttpClient] Service found without valid host: ${service.name}');
        // Try to use the service name as host if it looks like an IP
        if (service.name.contains('.') &&
            RegExp(r'^\d+\.\d+\.\d+\.\d+').hasMatch(service.name)) {
          host = service.name;
        } else {
          // Try other common attribute names for IP
          for (final key in ['host', 'address', 'ip_address']) {
            final data = service.attributes[key];
            if (data != null) {
              final converted = _convertAttributeToString(data);
              if (converted != null && converted.isNotEmpty) {
                host = converted;
                break;
              }
            }
          }

          if (host == null || host.isEmpty) {
            log.w(
                '[HttpClient] Could not determine host for service: ${service.name}');
            return;
          }
        }
      }

      // Validate port
      if (port <= 0) {
        log.w(
            '[HttpClient] Service found with invalid port: ${service.name}, port: $port');
        // Try to extract port from attributes
        final portData = service.attributes['port'];
        if (portData != null) {
          final portStr = _convertAttributeToString(portData);
          if (portStr != null) {
            port = int.tryParse(portStr) ?? 8080; // Default to 8080
          }
        } else {
          port = 8080; // Default port
        }
      }

      // Convert all service attributes to strings
      final convertedAttributes = <String, String>{};
      for (final entry in service.attributes.entries) {
        final convertedValue = _convertAttributeToString(entry.value);
        if (convertedValue != null) {
          convertedAttributes[entry.key] = convertedValue;
        }
      }

      final server = DiscoveredServer.fromService(
        name: service.name,
        host: host,
        port: port,
        serviceAttributes: convertedAttributes,
      );

      log.d('[HttpClient] Processed server: ${server.displayName}');
      emitDiscoveredServer(server);
    } catch (e, stack) {
      log.e('[HttpClient] Error processing complete service: $e',
          error: e, stackTrace: stack);
    }
  }

  /// Convert service attribute data to string, handling both List<int> and String types
  String? _convertAttributeToString(dynamic data) {
    if (data == null) return null;

    if (data is String) {
      return data;
    } else if (data is List<int>) {
      try {
        return String.fromCharCodes(data);
      } catch (e) {
        log.w('[HttpClient] Failed to convert List<int> to string: $e');
        return null;
      }
    } else if (data is Iterable<int>) {
      try {
        return String.fromCharCodes(data);
      } catch (e) {
        log.w('[HttpClient] Failed to convert Iterable<int> to string: $e');
        return null;
      }
    } else {
      // Fallback: convert to string representation
      return data.toString();
    }
  }

  void _stopDiscovery() {
    _discoveryTimer?.cancel();
    _discoveryTimer = null;

    if (_discovery != null) {
      _discovery!.stop();
      _discovery = null;
      log.d('[HttpClient] Stopped server discovery');
    }
  }

  Future<void> _disconnect() async {
    setConnected(false);
    _connectedServer = null;
    _client?.close();
    _client = null;
    log.d('[HttpClient] Disconnected from server');
  }

  @override
  String? get serverUrl => _connectedServer?.url;

  @override
  Future<void> startServer({
    required int port,
    required Function(String clientId) onClientConnected,
    required Function(String clientId) onClientDisconnected,
  }) async {
    throw UnsupportedError('Client mode cannot start server');
  }

  @override
  Future<void> startAdvertising(String serviceName) async {
    throw UnsupportedError('Client mode does not advertise');
  }

  @override
  Future<void> stopAdvertising() async {
    // No-op for client mode
  }

  @override
  Future<void> disposeImplementation() async {
    _stopDiscovery();
    await _disconnect();
  }
}
