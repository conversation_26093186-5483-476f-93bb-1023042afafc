import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:bonsoir/bonsoir.dart';
import 'package:draw_it/main.dart';
import 'network_service.dart';
import '../data/models/network_message.dart';
import '../data/models/discovered_server.dart';

/// HTTP client implementation for client mode (mobile platforms)
class HttpClientNetworkService extends BaseNetworkService {
  http.Client? _client;
  DiscoveredServer? _connectedServer;
  BonsoirDiscovery? _discovery;
  Timer? _discoveryTimer;

  @override
  Future<bool> connectToServer(DiscoveredServer server) async {
    try {
      // Test connection to server
      _client = http.Client();
      final response = await _client!
          .get(Uri.parse('${server.url}/'))
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 404) {
        // 404 is expected for root path, means server is running
        _connectedServer = server;
        setConnected(true);
        log.d('[HttpClient] Connected to server: ${server.displayName}');
        return true;
      } else {
        log.w(
            '[HttpClient] Unexpected response from server: ${response.statusCode}');
        return false;
      }
    } catch (e, stack) {
      log.e('[HttpClient] Failed to connect to server: $e',
          error: e, stackTrace: stack);
      setConnected(false);
      _connectedServer = null;
      _client?.close();
      _client = null;
      return false;
    }
  }

  @override
  Future<void> sendMessage(NetworkMessage message) async {
    if (!isConnected || _connectedServer == null || _client == null) {
      throw StateError('Not connected to server');
    }

    try {
      final url =
          Uri.parse('${_connectedServer!.url}/${message.type.endpoint}');
      final body = jsonEncode(message.toJson());

      log.d('[HttpClient] Sending ${message.type.endpoint}: $body');

      final response = await _client!
          .post(
            url,
            headers: {'Content-Type': 'application/json'},
            body: body,
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode != 202) {
        log.w('[HttpClient] Unexpected response: ${response.statusCode}');
      }
    } catch (e, stack) {
      log.e('[HttpClient] Failed to send message: $e',
          error: e, stackTrace: stack);

      // Check if we should disconnect
      if (e is SocketException || e is TimeoutException) {
        await _disconnect();
      }

      rethrow;
    }
  }

  @override
  Stream<DiscoveredServer> discoverServers() {
    _startDiscovery();
    return super.discoverServers();
  }

  void _startDiscovery() {
    if (_discovery != null) return;

    try {
      _discovery = BonsoirDiscovery(type: '_http._tcp');
      _discovery!.ready.then((_) {
        _discovery!.start();
        log.d('[HttpClient] Started server discovery');
      });

      _discovery!.eventStream!.listen((event) {
        if (event.type == BonsoirDiscoveryEventType.discoveryServiceFound) {
          _handleDiscoveredService(event.service!);
        }
      });

      // Stop discovery after a timeout
      _discoveryTimer = Timer(const Duration(seconds: 30), () {
        _stopDiscovery();
      });
    } catch (e, stack) {
      log.e('[HttpClient] Failed to start discovery: $e',
          error: e, stackTrace: stack);
    }
  }

  void _handleDiscoveredService(BonsoirService service) {
    try {
      // Check if this is a Draw It server
      if (!service.name.toLowerCase().contains('draw it')) {
        return;
      }

      // Extract IP from attributes
      final ipData = service.attributes['ip'];
      if (ipData == null) {
        log.w('[HttpClient] Service found without IP: ${service.name}');
        return;
      }

      // Convert IP data to string, handling both List<int> and String types
      final ip = _convertAttributeToString(ipData);
      if (ip == null) {
        log.w(
            '[HttpClient] Could not convert IP data to string: ${service.name}');
        return;
      }

      // Convert all service attributes to strings
      final convertedAttributes = <String, String>{};
      for (final entry in service.attributes.entries) {
        final convertedValue = _convertAttributeToString(entry.value);
        if (convertedValue != null) {
          convertedAttributes[entry.key] = convertedValue;
        }
      }

      final server = DiscoveredServer.fromService(
        name: service.name,
        host: ip,
        port: service.port,
        serviceAttributes: convertedAttributes,
      );

      log.d('[HttpClient] Discovered server: ${server.displayName}');
      emitDiscoveredServer(server);
    } catch (e, stack) {
      log.e('[HttpClient] Error handling discovered service: $e',
          error: e, stackTrace: stack);
    }
  }

  /// Convert service attribute data to string, handling both List<int> and String types
  String? _convertAttributeToString(dynamic data) {
    if (data == null) return null;

    if (data is String) {
      return data;
    } else if (data is List<int>) {
      try {
        return String.fromCharCodes(data);
      } catch (e) {
        log.w('[HttpClient] Failed to convert List<int> to string: $e');
        return null;
      }
    } else if (data is Iterable<int>) {
      try {
        return String.fromCharCodes(data);
      } catch (e) {
        log.w('[HttpClient] Failed to convert Iterable<int> to string: $e');
        return null;
      }
    } else {
      // Fallback: convert to string representation
      return data.toString();
    }
  }

  void _stopDiscovery() {
    _discoveryTimer?.cancel();
    _discoveryTimer = null;

    if (_discovery != null) {
      _discovery!.stop();
      _discovery = null;
      log.d('[HttpClient] Stopped server discovery');
    }
  }

  Future<void> _disconnect() async {
    setConnected(false);
    _connectedServer = null;
    _client?.close();
    _client = null;
    log.d('[HttpClient] Disconnected from server');
  }

  @override
  String? get serverUrl => _connectedServer?.url;

  @override
  Future<void> startServer({
    required int port,
    required Function(String clientId) onClientConnected,
    required Function(String clientId) onClientDisconnected,
  }) async {
    throw UnsupportedError('Client mode cannot start server');
  }

  @override
  Future<void> startAdvertising(String serviceName) async {
    throw UnsupportedError('Client mode does not advertise');
  }

  @override
  Future<void> stopAdvertising() async {
    // No-op for client mode
  }

  @override
  Future<void> disposeImplementation() async {
    _stopDiscovery();
    await _disconnect();
  }
}
