import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../platform/platform_detector.dart';
import '../platform/app_mode.dart';
import '../platform/platform_config.dart';
import '../drawing/drawing_point_handler.dart';
import '../drawing/server_point_handler.dart';
import '../drawing/client_point_handler.dart';
import '../drawing/drawing_renderer.dart';
import '../network/network_service.dart';
import '../network/http_server_service.dart';
import '../network/http_client_service.dart';

/// Provider for drawing point handler based on app mode
final drawingPointHandlerProvider = Provider<DrawingPointHandler>((ref) {
  final appMode = ref.read(appModeProvider);
  final config = ref.read(platformConfigProvider);
  
  switch (appMode) {
    case AppMode.server:
      final serverConfig = config as ServerPlatformConfig;
      return ServerDrawingPointHandler(
        port: serverConfig.defaultPort,
        serviceName: 'draw it server',
      );
    case AppMode.client:
      return ClientDrawingPointHandler();
  }
});

/// Provider for network service based on app mode
final networkServiceProvider = Provider<NetworkService>((ref) {
  final appMode = ref.read(appModeProvider);
  
  switch (appMode) {
    case AppMode.server:
      return HttpServerNetworkService();
    case AppMode.client:
      return HttpClientNetworkService();
  }
});

/// Provider for drawing renderer
final drawingRendererProvider = Provider<DrawingRenderer>((ref) {
  return const DrawingRendererImpl();
});

/// Provider for checking if the app is in server mode
final isServerModeProvider = Provider<bool>((ref) {
  return ref.read(appModeProvider) == AppMode.server;
});

/// Provider for checking if the app is in client mode
final isClientModeProvider = Provider<bool>((ref) {
  return ref.read(appModeProvider) == AppMode.client;
});

/// Provider for server-specific configuration
final serverConfigProvider = Provider<ServerPlatformConfig?>((ref) {
  final config = ref.read(platformConfigProvider);
  return config is ServerPlatformConfig ? config : null;
});

/// Provider for client-specific configuration
final clientConfigProvider = Provider<ClientPlatformConfig?>((ref) {
  final config = ref.read(platformConfigProvider);
  return config is ClientPlatformConfig ? config : null;
});
