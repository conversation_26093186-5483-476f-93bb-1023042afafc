import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:draw_it/main.dart';
import 'package:draw_it/common/window.dart';
import '../../../core/providers/core_providers.dart';
import '../../../core/data/models/drawing_point.dart';
import '../../../core/data/models/network_message.dart';
import '../../../core/drawing/drawing_point_handler.dart';
import '../../../core/drawing/drawing_renderer.dart';
import '../providers/server_providers.dart';

const handler = WindowHandler();

class ServerDrawingPage extends ConsumerStatefulWidget {
  const ServerDrawingPage({super.key});

  @override
  ConsumerState<ServerDrawingPage> createState() => _ServerDrawingPageState();
}

class _ServerDrawingPageState extends ConsumerState<ServerDrawingPage> {
  StreamSubscription<DrawingPoint>? _pointSubscription;
  StreamSubscription<ScreenCommandType>? _commandSubscription;
  StreamSubscription<SessionEvent>? _sessionSubscription;

  @override
  void initState() {
    super.initState();
    _initializeServer();
    _setupWindowHandling();
    _setupKeyboardHandling();
  }

  @override
  void dispose() {
    _pointSubscription?.cancel();
    _commandSubscription?.cancel();
    _sessionSubscription?.cancel();
    HardwareKeyboard.instance.removeHandler(_listenKeystroke);
    super.dispose();
  }

  Future<void> _initializeServer() async {
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider);
      await pointHandler.initialize();

      // Listen for drawing points
      _pointSubscription = pointHandler.pointStream.listen((point) {
        ref.read(serverDrawingStateProvider.notifier).addPoint(point);
      });

      // Listen for screen commands
      _commandSubscription = pointHandler.commandStream.listen((command) {
        _handleScreenCommand(command);
      });

      // Listen for session events
      _sessionSubscription = pointHandler.sessionStream.listen((event) {
        _handleSessionEvent(event);
      });

      log.d('[ServerDrawingPage] Server initialized successfully');
    } catch (e, stack) {
      log.e('[ServerDrawingPage] Failed to initialize server: $e', error: e, stackTrace: stack);
    }
  }

  void _setupWindowHandling() {
    handler.show();
    handler.addTransparency();

    if (Platform.isLinux) {
      _handleScreenCommand(ScreenCommandType.showWhiteboard);
    }
  }

  void _setupKeyboardHandling() {
    HardwareKeyboard.instance.addHandler(_listenKeystroke);
  }

  bool _listenKeystroke(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        handler.hide();
        return true;
      }
    }
    return false;
  }

  void _handleScreenCommand(ScreenCommandType command) {
    log.d('[ServerDrawingPage] Handling command: $command');

    switch (command) {
      case ScreenCommandType.clean:
        ref.read(serverDrawingStateProvider.notifier).clearDrawing();
        break;
      case ScreenCommandType.hide:
        handler.hide();
        break;
      case ScreenCommandType.show:
        handler.show();
        break;
      case ScreenCommandType.showWhiteboard:
        handler.show();
        handler.removeTransparency();
        ref.read(serverDrawingStateProvider.notifier).setBackgroundColor(Colors.white);
        break;
      case ScreenCommandType.hideWhiteboard:
        handler.show();
        handler.addTransparency();
        ref.read(serverDrawingStateProvider.notifier).setBackgroundColor(Colors.transparent);
        break;
    }
  }

  void _handleSessionEvent(SessionEvent event) {
    log.d('[ServerDrawingPage] Session event: ${event.type} for ${event.sessionId}');

    switch (event.type) {
      case SessionEventType.touchDown:
        ref.read(serverDrawingStateProvider.notifier).startNewStroke(event.sessionId);
        break;
      case SessionEventType.touchUp:
        ref.read(serverDrawingStateProvider.notifier).finishStroke(event.sessionId);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final drawingState = ref.watch(serverDrawingStateProvider);
    final renderer = ref.read(drawingRendererProvider);

    final targetSize = MediaQuery.sizeOf(context);

    return Container(
      color: drawingState.backgroundColor,
      child: DottedBorder(
        color: Colors.blue,
        strokeWidth: 6,
        dashPattern: const [12],
        child: CustomPaint(
          painter: DrawingCanvasPainter(
            completedStrokes: drawingState.completedStrokes,
            currentStroke: drawingState.currentStroke,
            sourceSize: drawingState.screenSize,
            targetSize: targetSize,
            renderer: renderer,
          ),
          child: Container(), // Full screen canvas
        ),
      ),
    );
  }
}
