import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/data/models/drawing_point.dart';

/// State for server drawing
class ServerDrawingState {
  const ServerDrawingState({
    this.completedStrokes = const [],
    this.currentStroke = const [],
    this.screenSize = const Size(1920, 1080),
    this.backgroundColor = Colors.transparent,
    this.currentSessionId,
  });

  final List<List<DrawingPoint>> completedStrokes;
  final List<DrawingPoint> currentStroke;
  final Size screenSize;
  final Color backgroundColor;
  final String? currentSessionId;

  ServerDrawingState copyWith({
    List<List<DrawingPoint>>? completedStrokes,
    List<DrawingPoint>? currentStroke,
    Size? screenSize,
    Color? backgroundColor,
    String? currentSessionId,
  }) {
    return ServerDrawingState(
      completedStrokes: completedStrokes ?? this.completedStrokes,
      currentStroke: currentStroke ?? this.currentStroke,
      screenSize: screenSize ?? this.screenSize,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      currentSessionId: currentSessionId ?? this.currentSessionId,
    );
  }
}

/// Notifier for server drawing state
class ServerDrawingStateNotifier extends StateNotifier<ServerDrawingState> {
  ServerDrawingStateNotifier() : super(const ServerDrawingState());

  /// Add a drawing point to the current stroke
  void addPoint(DrawingPoint point) {
    // Update screen size if provided
    // Note: In the network protocol, screen size comes with each point
    // but we'll use a default size for now
    
    final updatedStroke = [...state.currentStroke, point];
    state = state.copyWith(currentStroke: updatedStroke);
  }

  /// Start a new drawing stroke
  void startNewStroke(String sessionId) {
    // If there was a previous stroke, complete it first
    if (state.currentStroke.isNotEmpty) {
      finishStroke(state.currentSessionId ?? '');
    }
    
    state = state.copyWith(
      currentSessionId: sessionId,
      currentStroke: [],
    );
  }

  /// Finish the current stroke and add it to completed strokes
  void finishStroke(String sessionId) {
    if (state.currentStroke.isNotEmpty && 
        (state.currentSessionId == null || state.currentSessionId == sessionId)) {
      final updatedCompleted = [...state.completedStrokes, state.currentStroke];
      state = state.copyWith(
        completedStrokes: updatedCompleted,
        currentStroke: [],
        currentSessionId: null,
      );
    }
  }

  /// Clear all drawing
  void clearDrawing() {
    state = state.copyWith(
      completedStrokes: [],
      currentStroke: [],
      currentSessionId: null,
    );
  }

  /// Update screen size
  void updateScreenSize(Size size) {
    state = state.copyWith(screenSize: size);
  }

  /// Set background color
  void setBackgroundColor(Color color) {
    state = state.copyWith(backgroundColor: color);
  }
}

/// Provider for server drawing state
final serverDrawingStateProvider = StateNotifierProvider<ServerDrawingStateNotifier, ServerDrawingState>((ref) {
  return ServerDrawingStateNotifier();
});
