import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/data/models/discovered_server.dart';
import '../../../core/data/models/drawing_point.dart';

/// State for client functionality
class ClientState {
  const ClientState({
    this.discoveredServers = const [],
    this.connectedServer,
    this.isConnected = false,
    this.isDiscovering = false,
    this.connectionStatus = 'Not connected',
    this.currentColor = Colors.red,
    this.allowHandDrawing = true,
    this.allowWhiteboard = false,
    this.localStrokes = const [],
    this.currentStroke = const [],
    this.availableColors = const [
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.black,
      Colors.orange,
      Colors.purple,
    ],
  });

  final List<DiscoveredServer> discoveredServers;
  final DiscoveredServer? connectedServer;
  final bool isConnected;
  final bool isDiscovering;
  final String connectionStatus;
  final Color currentColor;
  final bool allowHandDrawing;
  final bool allowWhiteboard;
  final List<List<DrawingPoint>> localStrokes;
  final List<DrawingPoint> currentStroke;
  final List<Color> availableColors;

  ClientState copyWith({
    List<DiscoveredServer>? discoveredServers,
    DiscoveredServer? connectedServer,
    bool? isConnected,
    bool? isDiscovering,
    String? connectionStatus,
    Color? currentColor,
    bool? allowHandDrawing,
    bool? allowWhiteboard,
    List<List<DrawingPoint>>? localStrokes,
    List<DrawingPoint>? currentStroke,
    List<Color>? availableColors,
  }) {
    return ClientState(
      discoveredServers: discoveredServers ?? this.discoveredServers,
      connectedServer: connectedServer ?? this.connectedServer,
      isConnected: isConnected ?? this.isConnected,
      isDiscovering: isDiscovering ?? this.isDiscovering,
      connectionStatus: connectionStatus ?? this.connectionStatus,
      currentColor: currentColor ?? this.currentColor,
      allowHandDrawing: allowHandDrawing ?? this.allowHandDrawing,
      allowWhiteboard: allowWhiteboard ?? this.allowWhiteboard,
      localStrokes: localStrokes ?? this.localStrokes,
      currentStroke: currentStroke ?? this.currentStroke,
      availableColors: availableColors ?? this.availableColors,
    );
  }
}

/// Notifier for client state
class ClientStateNotifier extends StateNotifier<ClientState> {
  ClientStateNotifier() : super(const ClientState());

  /// Add a discovered server to the list
  void addDiscoveredServer(DiscoveredServer server) {
    if (!state.discoveredServers.any((s) => s.host == server.host && s.port == server.port)) {
      final updatedServers = [...state.discoveredServers, server];
      state = state.copyWith(discoveredServers: updatedServers);
    }
  }

  /// Clear discovered servers
  void clearDiscoveredServers() {
    state = state.copyWith(discoveredServers: []);
  }

  /// Set discovery status
  void setDiscovering(bool discovering) {
    state = state.copyWith(isDiscovering: discovering);
  }

  /// Connect to a server
  void connectToServer(DiscoveredServer server) {
    state = state.copyWith(
      connectedServer: server,
      isConnected: true,
      connectionStatus: 'Connected to ${server.displayName}',
    );
  }

  /// Disconnect from server
  void disconnect() {
    state = state.copyWith(
      connectedServer: null,
      isConnected: false,
      connectionStatus: 'Not connected',
    );
  }

  /// Set connection status
  void setConnectionStatus(String status) {
    state = state.copyWith(connectionStatus: status);
  }

  /// Change current drawing color
  void setCurrentColor(Color color) {
    state = state.copyWith(currentColor: color);
  }

  /// Toggle hand drawing mode
  void setAllowHandDrawing(bool allow) {
    state = state.copyWith(allowHandDrawing: allow);
  }

  /// Toggle whiteboard mode
  void setAllowWhiteboard(bool allow) {
    state = state.copyWith(allowWhiteboard: allow);
  }

  /// Add a point to the current stroke (for local preview)
  void addPointToCurrentStroke(DrawingPoint point) {
    final updatedStroke = [...state.currentStroke, point];
    state = state.copyWith(currentStroke: updatedStroke);
  }

  /// Finish the current stroke
  void finishCurrentStroke() {
    if (state.currentStroke.isNotEmpty) {
      final updatedStrokes = [...state.localStrokes, state.currentStroke];
      state = state.copyWith(
        localStrokes: updatedStrokes,
        currentStroke: [],
      );
    }
  }

  /// Clear all local drawing
  void clearLocalDrawing() {
    state = state.copyWith(
      localStrokes: [],
      currentStroke: [],
    );
  }

  /// Start a new stroke
  void startNewStroke() {
    state = state.copyWith(currentStroke: []);
  }
}

/// Provider for client state
final clientStateProvider = StateNotifierProvider<ClientStateNotifier, ClientState>((ref) {
  return ClientStateNotifier();
});
