import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:draw_it/main.dart';
import '../../../core/data/models/discovered_server.dart';
import '../../../core/drawing/client_point_handler.dart';
import '../../../core/providers/core_providers.dart';
import '../providers/client_providers.dart';

class ServerDiscoveryWidget extends ConsumerWidget {
  const ServerDiscoveryWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientState = ref.watch(clientStateProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'Looking for Draw It servers...',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            clientState.connectionStatus,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          
          if (clientState.isDiscovering)
            const CircularProgressIndicator()
          else
            ElevatedButton(
              onPressed: () => _startDiscovery(ref),
              child: const Text('Start Discovery'),
            ),
          
          const SizedBox(height: 32),
          
          if (clientState.discoveredServers.isNotEmpty) ...[
            Text(
              'Discovered Servers:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: clientState.discoveredServers.length,
                itemBuilder: (context, index) {
                  final server = clientState.discoveredServers[index];
                  return _ServerListItem(
                    server: server,
                    onTap: () => _connectToServer(ref, server),
                  );
                },
              ),
            ),
          ] else if (!clientState.isDiscovering) ...[
            const Text(
              'No servers found. Make sure a Draw It server is running on your network.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ],
      ),
    );
  }

  void _startDiscovery(WidgetRef ref) {
    ref.read(clientStateProvider.notifier).setDiscovering(true);
    ref.read(clientStateProvider.notifier).clearDiscoveredServers();
    ref.read(clientStateProvider.notifier).setConnectionStatus('Searching for servers...');
    
    // Discovery is automatically started when the client is initialized
    // This just updates the UI state
  }

  Future<void> _connectToServer(WidgetRef ref, DiscoveredServer server) async {
    try {
      ref.read(clientStateProvider.notifier).setConnectionStatus('Connecting to ${server.displayName}...');
      
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      final success = await pointHandler.connectToServer(server);
      
      if (success) {
        ref.read(clientStateProvider.notifier).connectToServer(server);
        log.d('[ServerDiscovery] Connected to server: ${server.displayName}');
      } else {
        ref.read(clientStateProvider.notifier).setConnectionStatus('Failed to connect to ${server.displayName}');
        log.w('[ServerDiscovery] Failed to connect to server: ${server.displayName}');
      }
    } catch (e, stack) {
      ref.read(clientStateProvider.notifier).setConnectionStatus('Error connecting to server');
      log.e('[ServerDiscovery] Error connecting to server: $e', error: e, stackTrace: stack);
    }
  }
}

class _ServerListItem extends StatelessWidget {
  const _ServerListItem({
    required this.server,
    required this.onTap,
  });

  final DiscoveredServer server;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.computer),
        title: Text(server.name),
        subtitle: Text('${server.host}:${server.port}'),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }
}
