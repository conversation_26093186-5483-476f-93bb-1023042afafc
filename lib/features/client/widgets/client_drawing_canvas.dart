import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:draw_it/main.dart';
import '../../../core/data/models/drawing_point.dart';
import '../../../core/drawing/client_point_handler.dart';
import '../../../core/drawing/drawing_renderer.dart';
import '../../../core/providers/core_providers.dart';
import '../providers/client_providers.dart';

class ClientDrawingCanvas extends ConsumerStatefulWidget {
  const ClientDrawingCanvas({super.key});

  @override
  ConsumerState<ClientDrawingCanvas> createState() => _ClientDrawingCanvasState();
}

class _ClientDrawingCanvasState extends ConsumerState<ClientDrawingCanvas> {
  String? _currentSessionId;

  @override
  Widget build(BuildContext context) {
    final clientState = ref.watch(clientStateProvider);
    final renderer = ref.read(drawingRendererProvider);
    
    return Container(
      color: Colors.white,
      child: GestureDetector(
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: CustomPaint(
          painter: DrawingCanvasPainter(
            completedStrokes: clientState.localStrokes,
            currentStroke: clientState.currentStroke,
            sourceSize: MediaQuery.sizeOf(context),
            targetSize: MediaQuery.sizeOf(context),
            renderer: renderer,
          ),
          child: Container(), // Full screen canvas
        ),
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    if (!ref.read(clientStateProvider).isConnected) return;

    // Generate new session ID
    _currentSessionId = DateTime.now().millisecondsSinceEpoch.toString();
    
    // Start new stroke locally
    ref.read(clientStateProvider.notifier).startNewStroke();
    
    // Send touch down to server
    _sendTouchDown();
    
    // Add first point
    _addPoint(details.localPosition, 0.5); // Default pressure for touch
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!ref.read(clientStateProvider).isConnected || _currentSessionId == null) return;
    
    // Estimate pressure based on platform
    double pressure = 0.5; // Default pressure
    
    if (Platform.isAndroid) {
      // On Android, we might have pressure information
      // For now, use default pressure
      pressure = 0.7;
    }
    
    _addPoint(details.localPosition, pressure);
  }

  void _onPanEnd(DragEndDetails details) {
    if (!ref.read(clientStateProvider).isConnected || _currentSessionId == null) return;
    
    // Finish stroke locally
    ref.read(clientStateProvider.notifier).finishCurrentStroke();
    
    // Send touch up to server
    _sendTouchUp();
    
    _currentSessionId = null;
  }

  void _addPoint(Offset position, double pressure) {
    if (_currentSessionId == null) return;
    
    final clientState = ref.read(clientStateProvider);
    final screenSize = MediaQuery.sizeOf(context);
    
    final point = DrawingPoint(
      x: position.dx,
      y: position.dy,
      pressure: pressure,
      colorHex: DrawingPointUtils.colorToHex(clientState.currentColor),
      timestamp: DateTime.now(),
    );
    
    // Add to local stroke for immediate feedback
    ref.read(clientStateProvider.notifier).addPointToCurrentStroke(point);
    
    // Send to server
    _sendPoint(point, screenSize);
  }

  Future<void> _sendPoint(DrawingPoint point, Size screenSize) async {
    if (_currentSessionId == null) return;
    
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      await pointHandler.handlePoint(point, _currentSessionId!, screenSize);
    } catch (e, stack) {
      log.e('[ClientDrawingCanvas] Failed to send point: $e', error: e, stackTrace: stack);
    }
  }

  Future<void> _sendTouchDown() async {
    if (_currentSessionId == null) return;
    
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      await pointHandler.handleTouchDown(_currentSessionId!);
    } catch (e, stack) {
      log.e('[ClientDrawingCanvas] Failed to send touch down: $e', error: e, stackTrace: stack);
    }
  }

  Future<void> _sendTouchUp() async {
    if (_currentSessionId == null) return;
    
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      await pointHandler.handleTouchUp(_currentSessionId!);
    } catch (e, stack) {
      log.e('[ClientDrawingCanvas] Failed to send touch up: $e', error: e, stackTrace: stack);
    }
  }
}
