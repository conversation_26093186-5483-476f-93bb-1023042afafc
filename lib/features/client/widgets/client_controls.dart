import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:draw_it/main.dart';
import '../../../core/data/models/network_message.dart';
import '../../../core/drawing/client_point_handler.dart';
import '../../../core/providers/core_providers.dart';
import '../providers/client_providers.dart';

class ClientControls extends ConsumerWidget {
  const ClientControls({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientState = ref.watch(clientStateProvider);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: const Border(
          bottom: BorderSide(color: Colors.grey),
        ),
      ),
      child: Column(
        children: [
          // Color selection
          Row(
            children: [
              const Text('Color: '),
              const SizedBox(width: 8),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: clientState.availableColors.map((color) {
                      return _ColorButton(
                        color: color,
                        isSelected: color == clientState.currentColor,
                        onTap: () => ref.read(clientStateProvider.notifier).setCurrentColor(color),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _ControlButton(
                icon: Icons.clear,
                label: 'Clear',
                onPressed: () => _sendCommand(ref, ScreenCommandType.clean),
              ),
              _ControlButton(
                icon: Icons.visibility,
                label: 'Show',
                onPressed: () => _sendCommand(ref, ScreenCommandType.show),
              ),
              _ControlButton(
                icon: Icons.visibility_off,
                label: 'Hide',
                onPressed: () => _sendCommand(ref, ScreenCommandType.hide),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Whiteboard controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _ControlButton(
                icon: Icons.dashboard,
                label: 'Whiteboard',
                onPressed: () => _sendCommand(ref, ScreenCommandType.showWhiteboard),
              ),
              _ControlButton(
                icon: Icons.dashboard_outlined,
                label: 'Transparent',
                onPressed: () => _sendCommand(ref, ScreenCommandType.hideWhiteboard),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Settings
          Row(
            children: [
              Expanded(
                child: SwitchListTile(
                  title: const Text('Hand Drawing'),
                  subtitle: const Text('Allow finger drawing'),
                  value: clientState.allowHandDrawing,
                  onChanged: (value) {
                    ref.read(clientStateProvider.notifier).setAllowHandDrawing(value);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _sendCommand(WidgetRef ref, ScreenCommandType command) async {
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      await pointHandler.handleScreenCommand(command);
      
      // Also clear local drawing if it's a clean command
      if (command == ScreenCommandType.clean) {
        ref.read(clientStateProvider.notifier).clearLocalDrawing();
      }
      
      log.d('[ClientControls] Sent command: $command');
    } catch (e, stack) {
      log.e('[ClientControls] Failed to send command: $e', error: e, stackTrace: stack);
    }
  }
}

class _ColorButton extends StatelessWidget {
  const _ColorButton({
    required this.color,
    required this.isSelected,
    required this.onTap,
  });

  final Color color;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.black : Colors.grey,
            width: isSelected ? 3 : 1,
          ),
        ),
      ),
    );
  }
}

class _ControlButton extends StatelessWidget {
  const _ControlButton({
    required this.icon,
    required this.label,
    required this.onPressed,
  });

  final IconData icon;
  final String label;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon),
          iconSize: 32,
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}
