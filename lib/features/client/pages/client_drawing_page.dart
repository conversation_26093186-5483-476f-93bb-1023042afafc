import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:draw_it/main.dart';
import '../../../core/data/models/discovered_server.dart';
import '../../../core/drawing/client_point_handler.dart';
import '../../../core/providers/core_providers.dart';
import '../providers/client_providers.dart';
import '../widgets/server_discovery_widget.dart';
import '../widgets/client_drawing_canvas.dart';
import '../widgets/client_controls.dart';

class ClientDrawingPage extends ConsumerStatefulWidget {
  const ClientDrawingPage({super.key});

  @override
  ConsumerState<ClientDrawingPage> createState() => _ClientDrawingPageState();
}

class _ClientDrawingPageState extends ConsumerState<ClientDrawingPage> {
  StreamSubscription<DiscoveredServer>? _discoverySubscription;

  @override
  void initState() {
    super.initState();
    _initializeClient();
  }

  @override
  void dispose() {
    _discoverySubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeClient() async {
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      await pointHandler.initialize();

      // Start discovering servers
      _discoverySubscription = pointHandler.discoverServers().listen((server) {
        ref.read(clientStateProvider.notifier).addDiscoveredServer(server);
      });

      log.d('[ClientDrawingPage] Client initialized successfully');
    } catch (e, stack) {
      log.e('[ClientDrawingPage] Failed to initialize client: $e', error: e, stackTrace: stack);
    }
  }

  @override
  Widget build(BuildContext context) {
    final clientState = ref.watch(clientStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Draw It - Client'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (clientState.isConnected)
            IconButton(
              icon: const Icon(Icons.link_off),
              onPressed: () => _disconnect(),
              tooltip: 'Disconnect',
            ),
        ],
      ),
      body: clientState.isConnected
        ? _buildDrawingInterface()
        : _buildServerDiscovery(),
    );
  }

  Widget _buildServerDiscovery() {
    return const ServerDiscoveryWidget();
  }

  Widget _buildDrawingInterface() {
    return Column(
      children: [
        // Connection status
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8.0),
          color: Colors.green.shade100,
          child: Consumer(
            builder: (context, ref, child) {
              final clientState = ref.watch(clientStateProvider);
              return Text(
                'Connected to: ${clientState.connectedServer?.displayName ?? 'Unknown'}',
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              );
            },
          ),
        ),

        // Drawing controls
        const ClientControls(),

        // Drawing canvas
        const Expanded(
          child: ClientDrawingCanvas(),
        ),
      ],
    );
  }

  Future<void> _disconnect() async {
    try {
      final pointHandler = ref.read(drawingPointHandlerProvider) as ClientDrawingPointHandler;
      await pointHandler.disconnect();
      ref.read(clientStateProvider.notifier).disconnect();

      // Restart discovery
      _discoverySubscription?.cancel();
      _discoverySubscription = pointHandler.discoverServers().listen((server) {
        ref.read(clientStateProvider.notifier).addDiscoveredServer(server);
      });

      log.d('[ClientDrawingPage] Disconnected from server');
    } catch (e, stack) {
      log.e('[ClientDrawingPage] Failed to disconnect: $e', error: e, stackTrace: stack);
    }
  }
}
