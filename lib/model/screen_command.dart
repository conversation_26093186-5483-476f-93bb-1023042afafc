import 'dart:ui';

import 'package:draw_it/model/point.dart';

sealed class ScreenCommand {
  const ScreenCommand();
}

class DrawCommand extends ScreenCommand {
  const DrawCommand(this.screenSize, this.points);

  final Size screenSize;
  final List<Point> points;

  static DrawCommand from<PERSON><PERSON>(Map<String, dynamic> json) {
    final List<dynamic> objects = json['points'];
    final points = objects.map((e) => Point.fromJson(e)).toList();

    final size = json['screen_size'] as Map;
    return DrawCommand(
      Size(size['width'], size['height']),
      points,
    );
  }
}

class DrawSingleCommand extends ScreenCommand {
  const DrawSingleCommand(this.screenSize, this.point, this.drawId);

  final Size screenSize;
  final Point point;
  final String drawId;

  static DrawSingleCommand fromJson(Map<String, dynamic> json) {
    final Map<String, dynamic> point = json['point'];

    final size = json['screen_size'] as Map;
    return DrawSingleCommand(
      Size(size['width'], size['height']),
      Point.fromJson(point),
      json['draw_id'],
    );
  }

  @override
  String toString() {
    return 'DrawSingleCommand{drawId: $drawId, point: $point, screenSize: $screenSize}';
  }
}

class CleanCommand extends ScreenCommand {
  const CleanCommand();
}

class TouchUpCommand extends ScreenCommand {
  const TouchUpCommand(this.drawId);

  final String drawId;

  static TouchUpCommand fromJson(Map<String, dynamic> json) {
    return TouchUpCommand(json['draw_id']);
  }

  @override
  String toString() {
    return 'TouchUpCommand{drawId: $drawId}';
  }
}

class TouchDownCommand extends ScreenCommand {
  const TouchDownCommand();
}

class ShowWindowCommand extends ScreenCommand {
  const ShowWindowCommand();
}

class HideWindowCommand extends ScreenCommand {
  const HideWindowCommand();
}

class ShowWhiteboardCommand extends ScreenCommand {
  const ShowWhiteboardCommand();
}

class HideWhiteboardCommand extends ScreenCommand {
  const HideWhiteboardCommand();
}
