import 'dart:convert' show utf8;
import 'dart:convert';
import 'dart:io';

import 'package:draw_it/main.dart';

const _tag = '[http][server]';

class WebServer {
  HttpServer? _server;

  bool get isRunning => _server != null;

  Future<void> start(int port) async {
    try {
      close();

      final server = await HttpServer.bind(InternetAddress.anyIPv6, port);
      _server = server;

      log.d(
          "$_tag Server running on IP : ${server.address} On Port : ${server.port}");
    } catch (e, stack) {
      log.e('$_tag Error: $e', error: e, stackTrace: stack);
    }
  }

  Stream<BasicRequest> listenRequests() async* {
    final server = _server;
    if (server == null) throw Exception('Server not started');

    await for (var request in server) {
      final body = await utf8.decodeStream(request);
      yield BasicRequest(request.uri, request.method, body);

      request.response
        ..statusCode = 202
        ..headers.contentType = ContentType(
          "text",
          "application/json",
          charset: "utf-8",
        )
        ..write('');

      await request.response.close();
    }
  }

  void close() {
    _server?.close(force: true);
  }
}

class BasicRequest {
  BasicRequest(this.uri, this.method, this.body)
      : resource = uri.path.split('/').last;

  final Uri uri;
  final String method;
  final String body;
  final String resource;

  @override
  String toString() {
    return 'BasicResponse{uri: $uri, method: $method, body: $body}';
  }
}
