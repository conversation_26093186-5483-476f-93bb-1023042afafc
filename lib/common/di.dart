import 'package:draw_it/common/network_discovery.dart';
import 'package:draw_it/common/point_repository.dart';
import 'package:draw_it/common/window.dart';

abstract class DiModule {
  static PointRepository? _instance;

  static PointRepository pointRepository() {
    return _instance ??= PointRepository();
  }

  static NetworkDiscovery networkDiscovery() {
    return NetworkDiscovery();
  }

  static WindowHandler windowHandler() {
    return const WindowHandler();
  }
}
