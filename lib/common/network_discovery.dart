import 'package:bonsoir/bonsoir.dart';
import 'package:draw_it/main.dart';

import 'dart:io';

class NetworkDiscovery {
  NetworkDiscovery();

  BonsoirBroadcast? _broadcast;

  Future<void> advertise() async {
    log.d('[zeroconf] starting discovery broadcast');

    stop();

    final ip = await _localIpV4();
    BonsoirService service = BonsoirService(
        name: 'draw it server',
        type: '_http._tcp',
        port: 8080,
        attributes: {'ip': ip});
    log.d('[zeroconf] Service: $service');

    BonsoirBroadcast broadcast =
        BonsoirBroadcast(service: service, printLogs: true);
    await broadcast.ready;
    await broadcast.start();

    _broadcast = broadcast;

    log.d(
        '[zeroconf] Started: ${broadcast.isReady} stopped: ${broadcast.isStopped}');
  }

  void stop() {
    _broadcast?.stop();
  }

  Future<String> _localIpV4() async {
    final interfaces = await NetworkInterface.list();

    return interfaces
        .firstWhere(
          (i) => i.addresses.any((a) => a.type == InternetAddressType.IPv4),
        )
        .addresses
        .firstWhere((a) => a.type == InternetAddressType.IPv4)
        .address;
  }
}
