import 'dart:async';
import 'dart:convert';

import 'package:draw_it/model/screen_command.dart';
import 'package:draw_it/common/web_server.dart';

class PointRepository {
  PointRepository() {
    _start();
  }

  final _controller = StreamController<ScreenCommand>.broadcast();
  StreamSubscription<BasicRequest>? _serverSubscription;

  Stream<ScreenCommand> get points {
    return _controller.stream;
  }

  void _start() async {
    final server = WebServer();
    server.start(8080);

    _serverSubscription = server.listenRequests().listen((request) {
      if (request.resource == 'draw') {
        final json = jsonDecode(request.body) as Map;
        final action = DrawCommand.fromJson(json['payload']);

        _controller.add(action);
      }
      if (request.resource == 'draw_single') {
        final json = jsonDecode(request.body) as Map;
        final action = DrawSingleCommand.fromJson(json['payload']);

        _controller.add(action);
      } else if (request.resource == 'touch_up') {
        final json = jsonDecode(request.body) as Map;
        final action = TouchUpCommand.fromJson(json['payload']);

        _controller.add(action);
      } else if (request.resource == 'touch_down') {
        _controller.add(const TouchDownCommand());
      } else if (request.resource == 'clean') {
        _controller.add(const CleanCommand());
      } else if (request.resource == 'hide') {
        _controller.add(const HideWindowCommand());
      } else if (request.resource == 'show') {
        _controller.add(const ShowWindowCommand());
      } else if (request.resource == 'show_whiteboard') {
        _controller.add(const ShowWhiteboardCommand());
      } else if (request.resource == 'hide_whiteboard') {
        _controller.add(const HideWhiteboardCommand());
      }
    });
  }

  void stop() {
    _serverSubscription?.cancel();
  }
}
